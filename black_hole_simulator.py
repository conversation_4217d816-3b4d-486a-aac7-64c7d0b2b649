"""
天体物理模拟程序 - 高级版
基于RTX 4060 Laptop GPU优化
支持多种天体类型、视野调整和时间流速控制
"""

import os
import sys
import math
import enum
import numpy as np
import time
import pygame
from pygame.locals import *
from OpenGL.GL import *
from OpenGL.GLU import *
from abc import ABC, abstractmethod

# 设置环境变量
os.environ['__NV_PRIME_RENDER_OFFLOAD'] = '1'
os.environ['__GLX_VENDOR_LIBRARY_NAME'] = 'nvidia'
os.environ['__VK_LAYER_NV_optimus'] = 'NVIDIA_only'

# 全局常量
G = 6.67430e-11  # 引力常数 (m^3 kg^-1 s^-2)
C = 2.99792458e8  # 光速 (m/s)
M_SUN = 1.989e30  # 太阳质量 (kg)
R_SUN = 6.957e8  # 太阳半径 (m)
SCALE_FACTOR = 1e-9  # 可视化缩放因子
AU = 1.496e11  # 天文单位 (m)

# 高级物理常数
PLANCK_CONSTANT = 6.62607015e-34  # 普朗克常数 (J⋅s)
BOLTZMANN_CONSTANT = 1.380649e-23  # 玻尔兹曼常数 (J/K)
STEFAN_BOLTZMANN = 5.670374419e-8  # 斯特藩-玻尔兹曼常数 (W⋅m⁻²⋅K⁻⁴)
ELECTRON_CHARGE = 1.602176634e-19  # 电子电荷 (C)
PROTON_MASS = 1.67262192369e-27  # 质子质量 (kg)
ELECTRON_MASS = 9.1093837015e-31  # 电子质量 (kg)

# 渲染优化常数
MAX_PARTICLES_RTX = 2000  # RTX模式下的最大粒子数
MAX_PARTICLES_STANDARD = 1000  # 标准模式下的最大粒子数
TRAIL_LENGTH_RTX = 50  # RTX模式下的轨迹长度
TRAIL_LENGTH_STANDARD = 25  # 标准模式下的轨迹长度

# 全局变量
CUDA_AVAILABLE = True
RTX_ENABLED = True

# 启用CUDA和RTX功能
try:
    import numba
    from numba import cuda, jit
    if cuda.is_available():
        print("CUDA加速已启用 - 使用RTX 4060 GPU")
        # 获取GPU信息
        for i, gpu in enumerate(cuda.gpus):
            print(f"GPU {i}: {gpu.name}")
            print(f"  计算能力: {gpu.compute_capability}")
            try:
                # 尝试获取内存信息
                memory_info = cuda.current_context().get_memory_info()
                print(f"  可用内存: {memory_info[0] / 1024**3:.1f} GB")
                print(f"  总内存: {memory_info[1] / 1024**3:.1f} GB")
            except:
                print("  内存信息: 无法获取")

        # 选择最佳GPU
        cuda.select_device(0)

        # 设置GPU内存管理
        cuda.current_context().memory_manager.defer_cleanup()

        # 检测RTX特性
        for gpu in cuda.gpus:
            gpu_name = gpu.name.decode() if isinstance(gpu.name, bytes) else gpu.name
            if 'RTX' in gpu_name or 'GeForce' in gpu_name:
                RTX_ENABLED = True
                print(f"RTX加速已启用: {gpu_name}")
                break

        if not RTX_ENABLED:
            print("未检测到RTX GPU，使用标准CUDA加速")

    else:
        CUDA_AVAILABLE = False
        RTX_ENABLED = False
        print("错误: CUDA不可用")
        print("请确保已安装CUDA驱动和Numba库")
        sys.exit(1)
except ImportError:
    CUDA_AVAILABLE = False
    RTX_ENABLED = False
    print("错误: Numba未安装")
    print("请运行: pip install numba")
    sys.exit(1)

# 强制GPU渲染检查
if not CUDA_AVAILABLE:
    print("错误: 程序需要GPU加速才能运行")
    print("请确保:")
    print("1. 已安装NVIDIA显卡驱动")
    print("2. 已安装CUDA工具包")
    print("3. 已安装numba库")
    sys.exit(1)

# CUDA加速函数
if CUDA_AVAILABLE:
    @cuda.jit
    def update_particle_positions_cuda(positions, velocities, dt, num_particles):
        """CUDA加速的粒子位置更新"""
        idx = cuda.grid(1)
        if idx < num_particles:
            positions[idx, 0] += velocities[idx, 0] * dt
            positions[idx, 1] += velocities[idx, 1] * dt
            positions[idx, 2] += velocities[idx, 2] * dt

    @cuda.jit
    def update_particle_velocities_cuda(positions, velocities, black_hole_pos, black_hole_mass, dt, num_particles):
        """CUDA加速的粒子速度更新（引力计算）"""
        idx = cuda.grid(1)
        if idx < num_particles:
            # 计算到黑洞的距离
            dx = positions[idx, 0] - black_hole_pos[0]
            dy = positions[idx, 1] - black_hole_pos[1]
            dz = positions[idx, 2] - black_hole_pos[2]

            distance = math.sqrt(dx*dx + dy*dy + dz*dz)

            if distance > 0.1:  # 避免除零
                # 计算引力加速度
                force_magnitude = G * black_hole_mass / (distance * distance)

                # 归一化方向向量
                dx_norm = dx / distance
                dy_norm = dy / distance
                dz_norm = dz / distance

                # 更新速度
                velocities[idx, 0] -= force_magnitude * dx_norm * dt
                velocities[idx, 1] -= force_magnitude * dy_norm * dt
                velocities[idx, 2] -= force_magnitude * dz_norm * dt

    @cuda.jit
    def calculate_orbital_motion_cuda(positions, velocities, black_hole_pos, black_hole_mass, dt, num_particles):
        """CUDA加速的轨道运动计算"""
        idx = cuda.grid(1)
        if idx < num_particles:
            # 计算到黑洞中心的向量
            dx = positions[idx, 0] - black_hole_pos[0]
            dy = positions[idx, 1] - black_hole_pos[1]
            dz = positions[idx, 2] - black_hole_pos[2]

            distance = math.sqrt(dx*dx + dy*dy + dz*dz)

            if distance > 0.1:
                # 计算轨道速度
                orbital_speed = math.sqrt(G * black_hole_mass / distance) * SCALE_FACTOR * 5.0

                # 计算切向速度方向
                angle = math.atan2(dy, dx)
                vx = -orbital_speed * math.sin(angle)
                vy = orbital_speed * math.cos(angle)

                # 更新速度
                velocities[idx, 0] = vx
                velocities[idx, 1] = vy
                velocities[idx, 2] *= 0.99  # 阻尼z方向速度

# 天体类型枚举
class CelestialType(enum.Enum):
    BLACK_HOLE_SCHWARZSCHILD = 0  # 史瓦西黑洞（无自旋）
    BLACK_HOLE_KERR = 1  # 克尔黑洞（有自旋）
    NEUTRON_STAR = 2  # 中子星
    WHITE_DWARF = 3  # 白矮星
    STAR_MAIN_SEQUENCE = 4  # 主序星
    STAR_RED_GIANT = 5  # 红巨星
    STAR_BLUE_GIANT = 6  # 蓝巨星
    PLANET_SYSTEM = 7  # 行星系统

# 基础天体类
class CelestialBody(ABC):
    """天体基类，所有天体类型的抽象基类"""

    def __init__(self, x=0, y=0, z=0, mass=1.0, name="未命名天体"):
        self.position = (x, y, z)
        self.mass = mass  # 质量 (kg)
        self.name = name
        self.quadric = gluNewQuadric()  # 创建二次曲面对象
        self.radius = 1.0  # 可视半径 (默认值，子类应覆盖)
        self.color = (1.0, 1.0, 1.0)  # 默认颜色 (白色)
        self.type = None  # 天体类型 (子类应设置)

    def __del__(self):
        """析构函数，释放OpenGL资源"""
        if self.quadric:
            gluDeleteQuadric(self.quadric)

    @abstractmethod
    def update(self, dt):
        """更新天体物理状态"""
        pass

    @abstractmethod
    def render(self):
        """渲染天体"""
        pass

    def get_info(self):
        """获取天体信息"""
        return {
            "name": self.name,
            "type": self.type.name if self.type else "未知",
            "mass": self.mass,
            "radius": self.radius / SCALE_FACTOR,  # 转换为实际物理单位
            "position": self.position
        }

# 黑洞基类
class BlackHoleBase(CelestialBody):
    """黑洞基类，所有黑洞类型的基类"""

    def __init__(self, x=0, y=0, z=0, mass=1e6, name="未命名黑洞"):
        super().__init__(x, y, z, mass, name)

        # 黑洞基本参数
        self.schwarzschild_radius = 2 * G * mass / (C * C)  # 史瓦西半径
        self.radius = max(2.0, self.schwarzschild_radius * SCALE_FACTOR)  # 事件视界可视半径

        # 粒子系统
        self.disk_particles = []
        self.jet_particles = []

        # 引力透镜效应参数
        self.gravitational_lensing_enabled = True
        self.lensing_strength = 1.0  # 引力透镜强度系数

        # 时间膨胀效应参数
        self.time_dilation_enabled = True
        self.time_dilation_factor = 1.0  # 时间膨胀系数

        # 引力波参数
        self.gravitational_waves_enabled = False
        self.wave_amplitude = 0.0  # 引力波振幅
        self.wave_frequency = 0.0  # 引力波频率

        # 事件视界特效
        self.event_horizon_glow = True  # 事件视界辉光效果
        self.horizon_glow_color = (0.1, 0.3, 0.8)  # 辉光颜色

        # 爱因斯坦环参数
        self.einstein_ring_enabled = True
        self.einstein_ring_radius = 0.0  # 将在子类中计算

    def calculate_einstein_ring(self):
        """计算爱因斯坦环半径"""
        # 爱因斯坦环半径公式: R_E = sqrt(4GM/c^2 * D_ls/(D_l*D_s))
        # 简化版本，假设观察者在无限远处
        self.einstein_ring_radius = math.sqrt(4.0 * G * self.mass / (C * C)) * SCALE_FACTOR * 20.0
        return self.einstein_ring_radius

    def render_particles(self, particles):
        """渲染粒子"""
        # 启用点平滑
        glEnable(GL_POINT_SMOOTH)
        glHint(GL_POINT_SMOOTH_HINT, GL_NICEST)

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 渲染粒子
        for particle in particles:
            # 应用时间膨胀效应 - 靠近黑洞的粒子看起来更慢
            time_dilation = 1.0
            if self.time_dilation_enabled and 'distance' in particle:
                # 计算时间膨胀因子
                r_ratio = particle['distance'] / (self.schwarzschild_radius * SCALE_FACTOR)
                if r_ratio > 1.0:  # 避免除以零或负值
                    time_dilation = math.sqrt(1.0 - 1.0 / r_ratio)
                    time_dilation = max(0.1, time_dilation)  # 限制最小值

                    # 存储时间膨胀因子
                    particle['time_dilation'] = time_dilation

            # 调整粒子大小 - 考虑引力透镜效应
            size_factor = 1.0
            if self.gravitational_lensing_enabled and 'distance' in particle:
                # 靠近黑洞的粒子看起来更大
                r_ratio = particle['distance'] / (self.schwarzschild_radius * SCALE_FACTOR)
                if r_ratio > 1.0 and r_ratio < 10.0:
                    size_factor = 1.0 + self.lensing_strength * (1.0 / r_ratio)

            # 设置粒子大小
            glPointSize(particle['size'] * size_factor)

            # 设置粒子颜色 - 考虑红移效应
            r, g, b = particle['color']
            if self.time_dilation_enabled and 'time_dilation' in particle and particle['time_dilation'] < 0.9:
                # 应用红移 - 靠近黑洞的粒子颜色偏红
                redshift = 1.0 - particle['time_dilation']
                r = min(1.0, r + redshift * 0.3)
                g = max(0.0, g - redshift * 0.2)
                b = max(0.0, b - redshift * 0.3)

            glColor4f(r, g, b, 1.0)
            glBegin(GL_POINTS)
            glVertex3f(*particle['position'])
            glEnd()

        # 渲染粒子轨迹
        glBegin(GL_LINES)
        for particle in particles:
            if len(particle['trail']) > 1:
                for i in range(len(particle['trail']) - 1):
                    # 轨迹颜色渐变
                    alpha = i / len(particle['trail'])

                    # 获取基础颜色
                    r, g, b = particle['color']

                    # 应用红移效应到轨迹
                    if self.time_dilation_enabled and 'time_dilation' in particle and particle['time_dilation'] < 0.9:
                        redshift = 1.0 - particle['time_dilation']
                        r = min(1.0, r + redshift * 0.3)
                        g = max(0.0, g - redshift * 0.2)
                        b = max(0.0, b - redshift * 0.3)

                    glColor4f(r, g, b, alpha * 0.5)
                    glVertex3f(*particle['trail'][i])
                    glVertex3f(*particle['trail'][i+1])
        glEnd()

        # 禁用混合
        glDisable(GL_BLEND)
        glDisable(GL_POINT_SMOOTH)

    def render_einstein_ring(self):
        """渲染爱因斯坦环"""
        if not self.einstein_ring_enabled:
            return

        # 如果爱因斯坦环半径未计算，则计算它
        if self.einstein_ring_radius <= 0:
            self.calculate_einstein_ring()

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试，以便环始终可见
        glDisable(GL_DEPTH_TEST)

        # 获取当前时间，用于动态效果
        current_time = time.time()

        # 渲染多重爱因斯坦环 - 模拟不同距离的光源产生的多个环
        num_rings = 3
        for ring_index in range(num_rings):
            # 环的基础半径
            base_radius = self.einstein_ring_radius * (0.9 + 0.2 * ring_index)

            # 环的宽度 - 使环更宽，更真实
            ring_width = self.radius * 0.2 * (1.0 + 0.5 * ring_index)

            # 环的颜色 - 不同环有不同颜色
            if ring_index == 0:  # 主环 - 蓝白色
                r, g, b = 0.7, 0.8, 1.0
            elif ring_index == 1:  # 次环 - 金色
                r, g, b = 1.0, 0.8, 0.4
            else:  # 外环 - 紫色
                r, g, b = 0.8, 0.4, 1.0

            # 环的透明度 - 外环更透明
            alpha = 0.4 * (1.0 - 0.2 * ring_index)

            # 渲染实心环 - 使用三角形带
            glBegin(GL_TRIANGLE_STRIP)
            segments = 128  # 增加分段数，使环更平滑
            for i in range(segments + 1):
                angle = 2.0 * math.pi * i / segments

                # 添加时间变化，使环有微小的波动
                radius_variation = 0.02 * math.sin(angle * 8.0 + current_time * 2.0 + ring_index * math.pi)

                # 内环半径
                inner_radius = base_radius - ring_width / 2.0
                inner_radius *= (1.0 + radius_variation)

                # 外环半径
                outer_radius = base_radius + ring_width / 2.0
                outer_radius *= (1.0 + radius_variation)

                # 计算内环点的位置
                x_inner = inner_radius * math.cos(angle)
                y_inner = inner_radius * math.sin(angle)
                z_inner = 0.0

                # 计算外环点的位置
                x_outer = outer_radius * math.cos(angle)
                y_outer = outer_radius * math.sin(angle)
                z_outer = 0.0

                # 颜色变化 - 使环有彩虹般的光谱效果
                color_variation = 0.2 * math.sin(angle * 4.0 + current_time + ring_index * 2.0)
                r_var = min(1.0, max(0.0, r + color_variation))
                g_var = min(1.0, max(0.0, g + color_variation))
                b_var = min(1.0, max(0.0, b + color_variation))

                # 设置内环点的颜色和透明度
                glColor4f(r_var, g_var, b_var, alpha * 0.7)
                glVertex3f(x_inner, y_inner, z_inner)

                # 设置外环点的颜色和透明度 - 外边缘更透明
                glColor4f(r_var, g_var, b_var, alpha * 0.3)
                glVertex3f(x_outer, y_outer, z_outer)
            glEnd()

        # 渲染引力弧 - 模拟背景星系被引力透镜效应扭曲的弧形
        self.render_gravitational_arcs(current_time)

        # 渲染背景星空扭曲效果
        self.render_background_distortion(current_time)

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_gravitational_arcs(self, current_time):
        """渲染引力弧 - 模拟背景星系被引力透镜效应扭曲的弧形"""
        # 引力弧数量
        num_arcs = 5

        # 引力弧基础半径 - 比爱因斯坦环稍大
        arc_base_radius = self.einstein_ring_radius * 1.2

        for i in range(num_arcs):
            # 弧的起始角度和跨度
            start_angle = 2.0 * math.pi * i / num_arcs + current_time * 0.1
            arc_span = math.pi * (0.2 + 0.3 * np.random.random())

            # 弧的半径 - 添加随机变化
            arc_radius = arc_base_radius * (0.9 + 0.2 * np.random.random())

            # 弧的宽度
            arc_width = self.radius * 0.3 * (0.8 + 0.4 * np.random.random())

            # 弧的颜色 - 随机蓝白色调
            r = 0.7 + 0.3 * np.random.random()
            g = 0.8 + 0.2 * np.random.random()
            b = 0.9 + 0.1 * np.random.random()
            alpha = 0.3 + 0.2 * np.random.random()

            # 渲染弧 - 使用三角形带
            glBegin(GL_TRIANGLE_STRIP)
            segments = 32
            for j in range(segments + 1):
                # 计算当前角度
                t = j / segments
                angle = start_angle + arc_span * t

                # 添加扭曲效果
                distortion = 0.1 * math.sin(t * math.pi * 4.0 + current_time * 2.0 + i)

                # 内弧半径
                inner_radius = arc_radius - arc_width / 2.0
                inner_radius *= (1.0 + distortion)

                # 外弧半径
                outer_radius = arc_radius + arc_width / 2.0
                outer_radius *= (1.0 + distortion)

                # 计算内弧点的位置
                x_inner = inner_radius * math.cos(angle)
                y_inner = inner_radius * math.sin(angle)
                z_inner = 0.0

                # 计算外弧点的位置
                x_outer = outer_radius * math.cos(angle)
                y_outer = outer_radius * math.sin(angle)
                z_outer = 0.0

                # 颜色渐变 - 从弧的一端到另一端
                color_factor = t
                r_var = r * (0.8 + 0.2 * color_factor)
                g_var = g * (0.8 + 0.2 * color_factor)
                b_var = b * (0.8 + 0.2 * color_factor)

                # 设置内弧点的颜色
                glColor4f(r_var, g_var, b_var, alpha)
                glVertex3f(x_inner, y_inner, z_inner)

                # 设置外弧点的颜色 - 外边缘更透明
                glColor4f(r_var, g_var, b_var, alpha * 0.5)
                glVertex3f(x_outer, y_outer, z_outer)
            glEnd()

    def render_background_distortion(self, current_time):
        """渲染背景星空扭曲效果 - 模拟引力透镜对背景星空的扭曲"""
        # 扭曲区域半径 - 比爱因斯坦环大
        distortion_radius = self.einstein_ring_radius * 2.0

        # 扭曲强度 - 与黑洞质量相关
        distortion_strength = 0.5 * (self.mass / 1e6)

        # 渲染扭曲网格 - 使用线段
        glLineWidth(1.0)

        # 径向线
        num_radial_lines = 24
        for i in range(num_radial_lines):
            angle = 2.0 * math.pi * i / num_radial_lines

            glBegin(GL_LINE_STRIP)
            segments = 20
            for j in range(segments):
                # 参数 t 从 0 到 1
                t = j / (segments - 1)

                # 基础半径 - 从爱因斯坦环到扭曲区域边缘
                base_radius = self.einstein_ring_radius + t * (distortion_radius - self.einstein_ring_radius)

                # 添加扭曲效果 - 靠近黑洞的线段更扭曲
                distortion_factor = distortion_strength * (1.0 - t) ** 2
                angle_distortion = distortion_factor * math.sin(angle * 2.0 + current_time)
                radius_distortion = distortion_factor * math.cos(angle * 3.0 + current_time * 1.5)

                # 计算扭曲后的角度和半径
                distorted_angle = angle + angle_distortion
                distorted_radius = base_radius * (1.0 + radius_distortion * 0.2)

                # 计算位置
                x = distorted_radius * math.cos(distorted_angle)
                y = distorted_radius * math.sin(distorted_angle)
                z = 0.0

                # 计算颜色 - 基于扭曲程度
                distortion_amount = math.sqrt(angle_distortion ** 2 + radius_distortion ** 2)
                r = 0.7 + 0.3 * distortion_amount
                g = 0.8 + 0.2 * distortion_amount
                b = 1.0
                alpha = 0.2 + 0.3 * (1.0 - t)

                glColor4f(r, g, b, alpha)
                glVertex3f(x, y, z)
            glEnd()

        # 环形线
        num_circular_lines = 8
        for i in range(num_circular_lines):
            # 基础半径 - 从爱因斯坦环到扭曲区域边缘
            t = i / (num_circular_lines - 1)
            base_radius = self.einstein_ring_radius + t * (distortion_radius - self.einstein_ring_radius)

            glBegin(GL_LINE_LOOP)
            segments = 64
            for j in range(segments):
                angle = 2.0 * math.pi * j / segments

                # 添加扭曲效果 - 靠近黑洞的环更扭曲
                distortion_factor = distortion_strength * (1.0 - t) ** 2
                radius_distortion = distortion_factor * math.sin(angle * 4.0 + current_time * 0.5)

                # 计算扭曲后的半径
                distorted_radius = base_radius * (1.0 + radius_distortion * 0.2)

                # 计算位置
                x = distorted_radius * math.cos(angle)
                y = distorted_radius * math.sin(angle)
                z = 0.0

                # 计算颜色 - 基于扭曲程度
                r = 0.7
                g = 0.8
                b = 1.0
                alpha = 0.2 + 0.3 * (1.0 - t)

                glColor4f(r, g, b, alpha)
                glVertex3f(x, y, z)
            glEnd()

    def render_event_horizon_glow(self):
        """渲染事件视界辉光效果"""
        if not self.event_horizon_glow:
            return

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 获取当前时间，用于动态效果
        current_time = time.time()

        # 渲染多层辉光 - 增加层数，使辉光更加平滑
        num_layers = 12
        for i in range(num_layers):
            # 辉光大小 - 使辉光范围更大
            glow_size = self.radius * (1.02 + 0.05 * i)

            # 辉光透明度 - 使内层更亮，外层更暗
            alpha = 0.15 * (1.0 - i / num_layers) ** 1.5

            # 辉光颜色 - 从内到外渐变
            # 内层偏蓝，外层偏紫
            r = self.horizon_glow_color[0] * (0.5 + 0.5 * i / num_layers)
            g = self.horizon_glow_color[1] * (1.0 - 0.3 * i / num_layers)
            b = self.horizon_glow_color[2]

            # 添加脉动效果
            pulse = 0.1 * math.sin(current_time * 1.5 + i * 0.2)
            glow_size *= (1.0 + pulse)

            # 渲染辉光
            glColor4f(r, g, b, alpha)
            gluSphere(self.quadric, glow_size, 64, 64)  # 增加细分数，使球体更平滑

        # 渲染事件视界边缘的蓝移/红移效果
        self.render_event_horizon_doppler_effect(current_time)

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_event_horizon_doppler_effect(self, current_time):
        """渲染事件视界的多普勒效应（蓝移/红移）"""
        # 渲染事件视界边缘的蓝移/红移环
        glLineWidth(3.0)

        # 渲染多个环，模拟不同深度的多普勒效应
        num_rings = 5
        for i in range(num_rings):
            # 环的半径 - 略大于事件视界
            ring_radius = self.radius * (1.001 + 0.002 * i)

            # 渲染环
            glBegin(GL_LINE_LOOP)
            segments = 128  # 增加分段数，使环更平滑
            for j in range(segments):
                angle = 2.0 * math.pi * j / segments

                # 计算多普勒效应 - 接近观察者的一侧蓝移，远离观察者的一侧红移
                doppler = math.cos(angle)

                # 添加时间变化，使效果更动态
                doppler_dynamic = doppler * (1.0 + 0.2 * math.sin(current_time * 2.0 + i * 0.5))

                # 计算颜色 - 蓝移/红移效果
                if doppler_dynamic > 0:  # 蓝移
                    r = 0.0
                    g = 0.3 + 0.7 * doppler_dynamic
                    b = 0.7 + 0.3 * doppler_dynamic
                    alpha = 0.7 * doppler_dynamic
                else:  # 红移
                    r = 0.7 + 0.3 * abs(doppler_dynamic)
                    g = 0.3 * abs(doppler_dynamic)
                    b = 0.0
                    alpha = 0.7 * abs(doppler_dynamic)

                glColor4f(r, g, b, alpha)

                # 计算位置
                x = ring_radius * math.cos(angle)
                y = ring_radius * math.sin(angle)
                z = 0.0

                glVertex3f(x, y, z)
            glEnd()

        # 渲染光线吸收效果 - 黑洞吞噬光线的可视化
        self.render_light_absorption_effect(current_time)

    def render_light_absorption_effect(self, current_time):
        """渲染光线被黑洞吸收的效果"""
        # 渲染从外部向事件视界流动的光线
        num_rays = 32
        for i in range(num_rays):
            # 光线起始角度
            angle = 2.0 * math.pi * i / num_rays + current_time * 0.2

            # 光线起点 - 远离黑洞
            start_radius = self.radius * 5.0
            start_x = start_radius * math.cos(angle)
            start_y = start_radius * math.sin(angle)
            start_z = (np.random.random() - 0.5) * self.radius * 2.0

            # 光线终点 - 事件视界
            end_radius = self.radius * 1.0

            # 计算光线弯曲路径
            glBegin(GL_LINE_STRIP)
            segments = 20
            for j in range(segments):
                # 参数 t 从 0 到 1
                t = j / (segments - 1)

                # 计算半径 - 非线性插值，模拟引力加速
                radius = start_radius * (1.0 - t) ** 2 + end_radius * t ** 2

                # 计算角度 - 添加弯曲，模拟引力弯曲
                curved_angle = angle + 0.5 * math.pi * t ** 2

                # 计算位置
                x = radius * math.cos(curved_angle)
                y = radius * math.sin(curved_angle)
                z = start_z * (1.0 - t) ** 2  # z坐标逐渐趋近于0

                # 计算颜色 - 随着接近黑洞逐渐变暗
                alpha = 0.5 * (1.0 - t) ** 0.5

                # 光线颜色 - 基于角度变化
                hue = (i / num_rays + current_time * 0.1) % 1.0
                r, g, b = self.hsv_to_rgb(hue, 0.8, 1.0)

                glColor4f(r, g, b, alpha)
                glVertex3f(x, y, z)
            glEnd()

    def hsv_to_rgb(self, h, s, v):
        """将HSV颜色转换为RGB颜色"""
        if s == 0.0:
            return v, v, v

        i = int(h * 6.0)
        f = (h * 6.0) - i
        p = v * (1.0 - s)
        q = v * (1.0 - s * f)
        t = v * (1.0 - s * (1.0 - f))
        i %= 6

        if i == 0:
            return v, t, p
        elif i == 1:
            return q, v, p
        elif i == 2:
            return p, v, t
        elif i == 3:
            return p, q, v
        elif i == 4:
            return t, p, v
        else:
            return v, p, q

# 史瓦西黑洞类
class SchwarzschildBlackHole(BlackHoleBase):
    """史瓦西黑洞 (无自旋)"""

    def __init__(self, x=0, y=0, z=0, mass=1e6, name="史瓦西黑洞"):
        super().__init__(x, y, z, mass, name)
        self.type = CelestialType.BLACK_HOLE_SCHWARZSCHILD
        self.spin_parameter = 0.0  # 无自旋

        # 计算光子球和最后稳定轨道
        self.photon_sphere_radius = self.calculate_photon_sphere()
        self.isco_radius = self.calculate_isco()

        # 创建吸积盘粒子
        if CUDA_AVAILABLE and RTX_ENABLED:
            self.generate_accretion_disk(1200)
        else:
            self.generate_accretion_disk(600)

    def calculate_photon_sphere(self):
        """计算光子球半径 (史瓦西黑洞)"""
        return 3.0 * self.schwarzschild_radius / 2.0 * SCALE_FACTOR

    def calculate_isco(self):
        """计算最后稳定圆轨道半径 (史瓦西黑洞)"""
        return 6.0 * self.schwarzschild_radius * SCALE_FACTOR

    def generate_accretion_disk(self, num_particles):
        """生成吸积盘粒子"""
        for i in range(num_particles):
            # 随机角度和距离
            angle = 2.0 * math.pi * i / num_particles
            distance = self.isco_radius + (10.0 * self.radius - self.isco_radius) * np.random.random()

            # 计算位置
            x = self.position[0] + distance * math.cos(angle)
            y = self.position[1] + distance * math.sin(angle)
            z = self.position[2] + (np.random.random() - 0.5) * 0.5

            # 计算轨道速度 (开普勒轨道)
            orbital_speed = math.sqrt(G * self.mass / distance) * SCALE_FACTOR * 5.0
            vx = -orbital_speed * math.sin(angle)
            vy = orbital_speed * math.cos(angle)
            vz = 0.0

            # 粒子颜色 - 偏红色
            r = 0.8 + 0.2 * np.random.random()
            g = 0.5 * np.random.random()
            b = 0.1 + 0.2 * np.random.random()

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': 2.0 + np.random.random(),
                'distance': distance,
                'angle': angle,
                'trail': []
            }

            self.disk_particles.append(particle)

    def update_accretion_disk_particles(self, dt):
        """更新吸积盘粒子"""
        # 使用CUDA加速粒子更新
        if CUDA_AVAILABLE:
            self._update_accretion_disk_particles_cuda(dt)
        else:
            self._update_accretion_disk_particles_cpu(dt)

    def _update_accretion_disk_particles_cuda(self, dt):
        """使用CUDA加速更新吸积盘粒子"""
        try:
            from numba import cuda
            import numpy as np

            # 更新所有粒子
            for particle in self.disk_particles:
                # 更新位置
                particle['position'][0] += particle['velocity'][0] * dt
                particle['position'][1] += particle['velocity'][1] * dt
                particle['position'][2] += particle['velocity'][2] * dt

                # 更新轨迹
                if len(particle['trail']) > 20:
                    particle['trail'].pop(0)
                particle['trail'].append(particle['position'].copy())

                # 计算到黑洞的距离 - 使用向量化操作
                pos = np.array(particle['position'])
                bh_pos = np.array(self.position)
                delta = pos - bh_pos
                distance = np.sqrt(np.sum(delta**2))

                # 更新距离和角度
                particle['distance'] = distance
                particle['angle'] = math.atan2(delta[1], delta[0])

                # 如果粒子太靠近黑洞，重置到外部区域
                if distance < self.isco_radius:
                    # 生成新角度
                    angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                    new_distance = self.radius * 10.0 * (0.8 + 0.2 * np.random.random())

                    particle['position'][0] = self.position[0] + new_distance * math.cos(angle)
                    particle['position'][1] = self.position[1] + new_distance * math.sin(angle)
                    particle['position'][2] = self.position[2] + (particle['id'] % 10) * 0.1 - 0.5

                    # 计算新的轨道速度
                    orbital_speed = math.sqrt(G * self.mass / new_distance) * SCALE_FACTOR * 5.0
                    particle['velocity'][0] = -orbital_speed * math.sin(angle)
                    particle['velocity'][1] = orbital_speed * math.cos(angle)
                    particle['velocity'][2] = 0.0

                    # 清除轨迹
                    particle['trail'] = []

                    # 更新粒子颜色 - 更鲜艳的颜色
                    r = 0.8 + 0.2 * np.random.random()
                    g = 0.5 * np.random.random()
                    b = 0.1 + 0.2 * np.random.random()
                    particle['color'] = (r, g, b)

                    # 更新粒子大小
                    particle['size'] = 2.0 + np.random.random()
        except Exception as e:
            print(f"CUDA加速失败，降级为CPU计算: {e}")
            self._update_accretion_disk_particles_cpu(dt)

    def _update_accretion_disk_particles_cpu(self, dt):
        """使用CPU更新吸积盘粒子"""
        for particle in self.disk_particles:
            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹
            if len(particle['trail']) > 20:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 计算到黑洞的距离
            dx = particle['position'][0] - self.position[0]
            dy = particle['position'][1] - self.position[1]
            dz = particle['position'][2] - self.position[2]
            distance = math.sqrt(dx*dx + dy*dy + dz*dz)

            # 更新距离和角度
            particle['distance'] = distance
            particle['angle'] = math.atan2(dy, dx)

            # 如果粒子太靠近黑洞，重置到外部区域
            if distance < self.isco_radius:
                # 生成新角度
                angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                new_distance = self.radius * 10.0 * (0.8 + 0.2 * np.random.random())

                particle['position'][0] = self.position[0] + new_distance * math.cos(angle)
                particle['position'][1] = self.position[1] + new_distance * math.sin(angle)
                particle['position'][2] = self.position[2] + (particle['id'] % 10) * 0.1 - 0.5

                # 计算新的轨道速度
                orbital_speed = math.sqrt(G * self.mass / new_distance) * SCALE_FACTOR * 5.0
                particle['velocity'][0] = -orbital_speed * math.sin(angle)
                particle['velocity'][1] = orbital_speed * math.cos(angle)
                particle['velocity'][2] = 0.0

                # 清除轨迹
                particle['trail'] = []

                # 更新粒子颜色
                r = 0.8 + 0.2 * np.random.random()
                g = 0.5 * np.random.random()
                b = 0.1 + 0.2 * np.random.random()
                particle['color'] = (r, g, b)

                # 更新粒子大小
                particle['size'] = 2.0 + np.random.random()

    def update(self, dt):
        """更新黑洞物理"""
        # 更新吸积盘粒子
        self.update_accretion_disk_particles(dt)

        # 更新喷流粒子
        if hasattr(self, 'spin_parameter') and self.spin_parameter > 0.6 and hasattr(self, 'update_jet_particles'):
            self.update_jet_particles(dt)

    def render(self):
        """渲染黑洞"""
        # 保存当前矩阵
        glPushMatrix()

        # 移动到黑洞位置
        glTranslatef(*self.position)

        # 渲染爱因斯坦环
        if self.einstein_ring_enabled:
            self.render_einstein_ring()

        # 渲染事件视界辉光
        if self.event_horizon_glow:
            self.render_event_horizon_glow()

        # 渲染事件视界 (黑色球体)
        glColor4f(0.0, 0.0, 0.0, 1.0)
        gluSphere(self.quadric, self.radius, 32, 32)

        # 渲染光子球 (半透明蓝色球体)
        glColor4f(0.0, 0.3, 0.8, 0.2)
        gluSphere(self.quadric, self.photon_sphere_radius, 32, 32)

        # 渲染吸积盘粒子
        self.render_particles(self.disk_particles)

        # 如果启用了引力波，渲染引力波效果
        if self.gravitational_waves_enabled and self.wave_amplitude > 0:
            self.render_gravitational_waves()

        # 恢复矩阵
        glPopMatrix()

    def render_gravitational_waves(self):
        """渲染引力波效果"""
        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 引力波颜色 - 淡紫色
        wave_color = (0.5, 0.3, 0.8, 0.1)

        # 当前时间
        current_time = time.time()

        # 渲染多个波环
        num_waves = 5
        for i in range(num_waves):
            # 波的半径
            phase = (current_time * self.wave_frequency + i / num_waves) % 1.0
            wave_radius = self.radius * 5.0 + phase * self.radius * 20.0

            # 波的透明度
            alpha = 0.1 * (1.0 - phase)

            # 渲染波环
            glColor4f(*wave_color[:3], alpha)
            glBegin(GL_LINE_LOOP)
            segments = 64
            for j in range(segments):
                angle = 2.0 * math.pi * j / segments

                # 添加波动效果
                distortion = self.wave_amplitude * math.sin(angle * 4.0 + current_time * 2.0)
                radius = wave_radius * (1.0 + distortion * 0.05)

                x = radius * math.cos(angle)
                y = radius * math.sin(angle)
                z = 0.0
                glVertex3f(x, y, z)
            glEnd()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

# 克尔黑洞类
class KerrBlackHole(BlackHoleBase):
    """克尔黑洞 (有自旋)"""

    def __init__(self, x=0, y=0, z=0, mass=1e6, spin=0.998, name="克尔黑洞"):
        super().__init__(x, y, z, mass, name)
        self.type = CelestialType.BLACK_HOLE_KERR
        self.spin_parameter = max(0.0, min(0.998, spin))  # 自旋参数 (0-0.998)

        # 基于Kerr度规计算光子球和最后稳定轨道
        a = self.spin_parameter * self.schwarzschild_radius / 2.0
        self.photon_sphere_radius = self.calculate_photon_sphere(a)
        self.isco_radius = self.calculate_isco(a)

        # 创建吸积盘粒子
        self.disk_particles = []
        if CUDA_AVAILABLE and RTX_ENABLED:
            self.generate_accretion_disk(1500)
            self.generate_jets(400)
        else:
            self.generate_accretion_disk(800)
            self.generate_jets(200)

    def calculate_photon_sphere(self, a):
        """计算光子球半径 (克尔黑洞)"""
        # 简化的光子球计算，考虑自旋参数
        # 实际上，克尔黑洞的光子球是非球形的，这里做了简化
        r_ph = 2 * (1 + math.cos(2/3 * math.acos(-self.spin_parameter)))
        return r_ph * self.schwarzschild_radius / 2.0 * SCALE_FACTOR

    def calculate_isco(self, a):
        """计算最后稳定圆轨道半径 (克尔黑洞)"""
        # 简化的ISCO计算，考虑自旋参数
        # 对于顺向旋转的轨道
        Z1 = 1 + (1 - self.spin_parameter**2)**(1/3) * ((1 + self.spin_parameter)**(1/3) + (1 - self.spin_parameter)**(1/3))
        Z2 = math.sqrt(3 * self.spin_parameter**2 + Z1**2)
        r_isco = 3 + Z2 - math.sqrt((3 - Z1) * (3 + Z1 + 2*Z2))
        return r_isco * self.schwarzschild_radius / 2.0 * SCALE_FACTOR

    def generate_accretion_disk(self, num_particles):
        """生成吸积盘粒子"""
        # 清空现有粒子
        self.disk_particles = []

        # 吸积盘参数
        inner_radius = self.isco_radius  # 内边缘 - 最后稳定轨道
        outer_radius = 15.0 * self.radius  # 外边缘

        # 吸积盘厚度 - 与自旋参数相关
        # 高自旋黑洞的吸积盘更薄，低自旋黑洞的吸积盘更厚
        if hasattr(self, 'spin_parameter'):
            base_thickness = 0.5 * (1.0 - 0.5 * self.spin_parameter)
        else:
            base_thickness = 0.5

        # 吸积盘温度分布 - 越靠近黑洞温度越高
        # 温度与距离的关系：T ∝ r^(-3/4)
        max_temp = 10000  # K，内边缘最高温度

        # 生成粒子
        for i in range(num_particles):
            # 确定粒子在吸积盘中的径向位置
            # 使用非线性分布，使内部区域粒子密度更高
            radius_factor = np.random.random() ** 0.5  # 非线性分布
            distance = inner_radius + (outer_radius - inner_radius) * radius_factor

            # 确定角度 - 均匀分布
            angle = 2.0 * math.pi * i / num_particles

            # 计算吸积盘在该半径处的厚度
            # 厚度随半径增加而增加，符合真实吸积盘的形态
            thickness_factor = 0.5 + 0.5 * (distance - inner_radius) / (outer_radius - inner_radius)
            thickness = base_thickness * thickness_factor * self.radius

            # 计算z坐标 - 使吸积盘有厚度
            # 使用高斯分布，使粒子集中在中心平面
            z_offset = np.random.normal(0, thickness * 0.3)

            # 计算位置
            x = self.position[0] + distance * math.cos(angle)
            y = self.position[1] + distance * math.sin(angle)
            z = self.position[2] + z_offset

            # 计算轨道速度 (考虑相对论效应)
            orbital_speed = math.sqrt(G * self.mass / distance) * SCALE_FACTOR * 5.0

            # 自旋效应：靠近黑洞的粒子受到框拖效应影响，速度增加
            if hasattr(self, 'spin_parameter') and distance < self.radius * 8.0:
                # 框拖效应强度与自旋参数和距离成反比
                frame_dragging = self.spin_parameter * (1.0 - distance / (self.radius * 8.0)) * 0.3
                orbital_speed *= (1.0 + frame_dragging)

                # 添加一些垂直方向的速度分量，模拟湍流
                vz = orbital_speed * 0.05 * frame_dragging * (np.random.random() - 0.5)
            else:
                vz = 0.0

            # 基本轨道速度
            vx = -orbital_speed * math.sin(angle)
            vy = orbital_speed * math.cos(angle)

            # 计算温度 - 基于距离
            temperature = max_temp * (inner_radius / distance) ** (3.0/4.0)

            # 基于温度计算颜色 - 使用黑体辐射颜色
            r, g, b = self.temperature_to_rgb(temperature)

            # 添加一些随机变化，使颜色更自然
            r = min(1.0, r * (0.9 + 0.2 * np.random.random()))
            g = min(1.0, g * (0.9 + 0.2 * np.random.random()))
            b = min(1.0, b * (0.9 + 0.2 * np.random.random()))

            # 粒子类型 - 添加不同类型的粒子，模拟吸积盘的不同组成部分
            particle_type = np.random.choice(['normal', 'hot_spot', 'dust'], p=[0.8, 0.15, 0.05])

            # 根据粒子类型调整属性
            if particle_type == 'hot_spot':
                # 热点 - 更亮，更大
                r = min(1.0, r * 1.5)
                g = min(1.0, g * 1.3)
                b = min(1.0, b * 1.2)
                size = 3.0 + np.random.random() * 2.0
                # 热点寿命较短
                lifetime = 2.0 + np.random.random() * 3.0
            elif particle_type == 'dust':
                # 尘埃 - 更暗，更小，更红
                r = r * 0.8
                g = g * 0.6
                b = b * 0.4
                size = 1.0 + np.random.random()
                # 尘埃寿命较长
                lifetime = 10.0 + np.random.random() * 5.0
            else:
                # 普通粒子
                size = 2.0 + np.random.random()
                lifetime = 5.0 + np.random.random() * 5.0

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': size,
                'distance': distance,
                'angle': angle,
                'type': particle_type,
                'temperature': temperature,
                'lifetime': 0.0,  # 当前寿命
                'max_lifetime': lifetime,  # 最大寿命
                'trail': []
            }

            self.disk_particles.append(particle)

    def temperature_to_rgb(self, temperature):
        """将温度转换为RGB颜色 (基于黑体辐射)"""
        # 温度范围
        min_temp = 1000  # K
        max_temp = 10000  # K

        # 将温度限制在有效范围内
        temp = max(min_temp, min(max_temp, temperature))

        # 归一化温度 (0-1)
        t = (temp - min_temp) / (max_temp - min_temp)

        # 基于温度计算RGB
        # 低温 -> 红色
        # 中温 -> 黄色/白色
        # 高温 -> 蓝白色
        if t < 0.33:  # 低温
            r = 1.0
            g = t * 3.0
            b = 0.0
        elif t < 0.66:  # 中温
            r = 1.0
            g = 1.0
            b = (t - 0.33) * 3.0
        else:  # 高温
            r = 1.0
            g = 1.0
            b = 1.0
            # 最高温度时偏蓝
            if t > 0.9:
                r = max(0.0, r - (t - 0.9) * 5.0)
                g = max(0.0, g - (t - 0.9) * 5.0)

        return r, g, b

    def generate_jets(self, num_particles):
        """生成喷流粒子"""
        # 只有高自旋黑洞才产生喷流
        if self.spin_parameter < 0.6:
            return

        self.jet_particles = []
        for i in range(num_particles):
            # 随机方向 (上/下)
            direction = 1 if i < num_particles / 2 else -1

            # 随机距离
            distance = 2.0 + 3.0 * np.random.random()

            # 随机角度和半径
            angle = 2.0 * math.pi * np.random.random()
            radius = 0.5 * np.random.random()

            # 计算位置
            x = self.position[0] + radius * math.cos(angle)
            y = self.position[1] + radius * math.sin(angle)
            z = self.position[2] + direction * distance

            # 计算速度 (与自旋参数相关)
            speed = (0.1 + 0.2 * np.random.random()) * C * SCALE_FACTOR * 0.01 * self.spin_parameter
            vx = 0.0
            vy = 0.0
            vz = direction * speed

            # 粒子颜色
            if direction > 0:  # 蓝色喷流
                r = 0.2 * np.random.random()
                g = 0.4 + 0.4 * np.random.random()
                b = 0.7 + 0.3 * np.random.random()
            else:  # 红色喷流
                r = 0.7 + 0.3 * np.random.random()
                g = 0.4 + 0.4 * np.random.random()
                b = 0.2 * np.random.random()

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': 1.5 + np.random.random(),
                'direction': direction,
                'trail': []
            }

            self.jet_particles.append(particle)

    def update_accretion_disk_particles(self, dt):
        """更新吸积盘粒子"""
        # 使用CUDA加速粒子更新
        if CUDA_AVAILABLE:
            self._update_accretion_disk_particles_cuda(dt)
        else:
            self._update_accretion_disk_particles_cpu(dt)

    def _update_accretion_disk_particles_cuda(self, dt):
        """使用CUDA加速更新吸积盘粒子"""
        try:
            from numba import cuda
            import numpy as np

            # 更新所有粒子
            for particle in self.disk_particles:
                # 更新位置
                particle['position'][0] += particle['velocity'][0] * dt
                particle['position'][1] += particle['velocity'][1] * dt
                particle['position'][2] += particle['velocity'][2] * dt

                # 更新轨迹
                if len(particle['trail']) > 20:
                    particle['trail'].pop(0)
                particle['trail'].append(particle['position'].copy())

                # 计算到黑洞的距离 - 使用向量化操作
                pos = np.array(particle['position'])
                bh_pos = np.array(self.position)
                delta = pos - bh_pos
                distance = np.sqrt(np.sum(delta**2))

                # 更新距离和角度
                particle['distance'] = distance
                particle['angle'] = math.atan2(delta[1], delta[0])

                # 如果粒子太靠近黑洞，重置到外部区域
                if distance < self.isco_radius:
                    # 生成新角度
                    angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                    new_distance = self.radius * 10.0 * (0.8 + 0.2 * np.random.random())

                    particle['position'][0] = self.position[0] + new_distance * math.cos(angle)
                    particle['position'][1] = self.position[1] + new_distance * math.sin(angle)
                    particle['position'][2] = self.position[2] + (particle['id'] % 10) * 0.1 - 0.5

                    # 计算新的轨道速度
                    orbital_speed = math.sqrt(G * self.mass / new_distance) * SCALE_FACTOR * 5.0
                    particle['velocity'][0] = -orbital_speed * math.sin(angle)
                    particle['velocity'][1] = orbital_speed * math.cos(angle)
                    particle['velocity'][2] = 0.0

                    # 清除轨迹
                    particle['trail'] = []

                    # 更新粒子颜色 - 更鲜艳的颜色
                    r = 0.8 + 0.2 * np.random.random()
                    g = 0.5 * np.random.random()
                    b = 0.1 + 0.2 * np.random.random()
                    particle['color'] = (r, g, b)

                    # 更新粒子大小
                    particle['size'] = 2.0 + np.random.random()
        except Exception as e:
            print(f"CUDA加速失败，降级为CPU计算: {e}")
            self._update_accretion_disk_particles_cpu(dt)

    def _update_accretion_disk_particles_cpu(self, dt):
        """使用CPU更新吸积盘粒子"""
        # 获取当前时间，用于动态效果
        current_time = time.time()

        # 吸积盘不稳定性参数 - 模拟吸积盘的全局不稳定性
        # 使用正弦波模拟吸积盘的周期性波动
        global_instability = 0.2 * math.sin(current_time * 0.5)

        # 吸积盘湍流参数 - 随时间变化
        turbulence_strength = 0.05 + 0.03 * math.sin(current_time * 0.3)

        # 热点爆发概率 - 随机产生热点爆发
        hot_spot_eruption_probability = 0.001 * dt  # 每秒0.1%的概率

        # 更新所有粒子
        for particle in self.disk_particles:
            # 检查粒子是否有寿命属性，如果没有，添加默认值
            if 'lifetime' not in particle:
                particle['lifetime'] = 0.0
                particle['max_lifetime'] = 5.0 + 5.0 * np.random.random()
                particle['type'] = 'normal'

            # 更新粒子寿命
            particle['lifetime'] += dt

            # 检查粒子是否超过最大寿命
            if particle['lifetime'] > particle['max_lifetime']:
                # 重新生成粒子
                self.regenerate_disk_particle(particle, current_time)
                continue

            # 计算到黑洞的距离
            dx = particle['position'][0] - self.position[0]
            dy = particle['position'][1] - self.position[1]
            dz = particle['position'][2] - self.position[2]
            distance = math.sqrt(dx*dx + dy*dy + dz*dz)

            # 计算当前角度
            current_angle = math.atan2(dy, dx)

            # 框拖效应：靠近黑洞的粒子受到更强的引力和时空扭曲
            if distance < self.radius * 12.0:  # 增加框拖效应的影响范围
                # 框拖效应强度与自旋参数和距离成反比
                frame_dragging = self.spin_parameter * (1.0 - distance / (self.radius * 12.0)) * 0.4

                # 应用框拖效应到角度 - 使粒子旋转更加明显
                angle_change = frame_dragging * dt * 6.0
                current_angle += angle_change

                # 修改粒子速度，模拟框拖效应
                orbital_speed = math.sqrt(G * self.mass / distance) * SCALE_FACTOR * 5.0

                # 自旋效应增加切向速度
                orbital_speed *= (1.0 + frame_dragging * 1.8)

                # 添加全局不稳定性 - 模拟吸积盘的波动
                orbital_speed *= (1.0 + global_instability * (1.0 - distance / (self.radius * 12.0)))

                # 更新基本轨道速度
                particle['velocity'][0] = -orbital_speed * math.sin(current_angle)
                particle['velocity'][1] = orbital_speed * math.cos(current_angle)

                # 添加径向速度分量，模拟椭圆轨道和吸积过程
                radial_component = frame_dragging * orbital_speed * 0.2
                # 添加随机变化，模拟湍流
                radial_component *= (1.0 + turbulence_strength * (np.random.random() - 0.5))
                particle['velocity'][0] += radial_component * math.cos(current_angle)
                particle['velocity'][1] += radial_component * math.sin(current_angle)

                # 添加垂直方向的速度分量，模拟吸积盘的三维结构
                vertical_amplitude = 0.1 * self.spin_parameter
                vertical_frequency = 4.0 + 2.0 * np.random.random()
                vertical_phase = current_angle * vertical_frequency + current_time
                vertical_component = vertical_amplitude * orbital_speed * math.sin(vertical_phase)
                # 添加随机湍流
                vertical_component += turbulence_strength * orbital_speed * (np.random.random() - 0.5)
                particle['velocity'][2] = vertical_component

                # 计算温度 - 基于距离和湍流
                base_temperature = 10000 * (self.isco_radius / distance) ** (3.0/4.0)
                temperature_variation = 0.2 * base_temperature * (np.random.random() - 0.5)
                current_temperature = base_temperature + temperature_variation

                # 基于温度计算颜色
                r, g, b = self.temperature_to_rgb(current_temperature)

                # 根据粒子类型调整颜色
                if 'type' in particle and particle['type'] == 'hot_spot':
                    # 热点 - 更亮，颜色更白
                    lifetime_factor = 1.0 - particle['lifetime'] / particle['max_lifetime']
                    r = min(1.0, r + 0.3 * lifetime_factor)
                    g = min(1.0, g + 0.3 * lifetime_factor)
                    b = min(1.0, b + 0.3 * lifetime_factor)

                    # 热点大小随寿命变化
                    size_factor = 1.0 + 2.0 * lifetime_factor
                    particle['size'] = 2.0 * size_factor

                    # 热点可能产生爆发
                    if np.random.random() < 0.05 * dt:
                        # 添加爆发效果 - 速度突然增加
                        burst_factor = 1.5 + 0.5 * np.random.random()
                        particle['velocity'][0] *= burst_factor
                        particle['velocity'][1] *= burst_factor
                        particle['velocity'][2] *= burst_factor

                        # 爆发后热点变得更亮
                        r = min(1.0, r * 1.5)
                        g = min(1.0, g * 1.5)
                        b = min(1.0, b * 1.5)

                elif 'type' in particle and particle['type'] == 'dust':
                    # 尘埃 - 更暗，更红
                    r = r * 0.8
                    g = g * 0.6
                    b = b * 0.4

                    # 尘埃大小较小
                    particle['size'] = 1.0 + 0.5 * np.random.random()

                    # 尘埃受湍流影响更大
                    turbulence_effect = turbulence_strength * 2.0 * (np.random.random() - 0.5)
                    particle['velocity'][0] += particle['velocity'][0] * turbulence_effect
                    particle['velocity'][1] += particle['velocity'][1] * turbulence_effect
                    particle['velocity'][2] += particle['velocity'][2] * turbulence_effect

                else:  # 普通粒子
                    # 普通粒子可能随机变成热点 - 模拟热点爆发
                    if np.random.random() < hot_spot_eruption_probability and distance < self.radius * 5.0:
                        particle['type'] = 'hot_spot'
                        particle['lifetime'] = 0.0
                        particle['max_lifetime'] = 1.0 + 2.0 * np.random.random()

                        # 热点爆发 - 速度突然增加
                        burst_factor = 1.5 + 1.0 * np.random.random()
                        particle['velocity'][0] *= burst_factor
                        particle['velocity'][1] *= burst_factor
                        particle['velocity'][2] *= burst_factor

                        # 热点颜色更亮
                        r = min(1.0, r * 1.5)
                        g = min(1.0, g * 1.5)
                        b = min(1.0, b * 1.5)

                        # 热点大小更大
                        particle['size'] = 3.0 + 2.0 * np.random.random()
                    else:
                        # 普通粒子大小
                        heat_factor = 1.0 - distance / (self.radius * 12.0)
                        particle['size'] = 2.0 + 2.0 * heat_factor + 0.5 * np.random.random()

                # 更新粒子颜色
                particle['color'] = (r, g, b)

                # 存储角度变化率，用于可视化旋转速度
                particle['rotation_speed'] = angle_change / dt

            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹
            # 轨迹长度与自旋参数和粒子类型相关
            if 'type' in particle and particle['type'] == 'hot_spot':
                max_trail_length = int(50 + 30 * self.spin_parameter)  # 热点轨迹更长
            else:
                max_trail_length = int(30 + 20 * self.spin_parameter)

            if len(particle['trail']) > max_trail_length:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 更新距离和角度
            dx = particle['position'][0] - self.position[0]
            dy = particle['position'][1] - self.position[1]
            dz = particle['position'][2] - self.position[2]
            distance = math.sqrt(dx*dx + dy*dy + dz*dz)
            particle['distance'] = distance
            particle['angle'] = math.atan2(dy, dx)

            # 如果粒子太靠近黑洞或飞得太远，重置到外部区域
            if distance < self.isco_radius or distance > self.radius * 20.0:
                self.regenerate_disk_particle(particle, current_time)

    def regenerate_disk_particle(self, particle, current_time=None):
        """重新生成吸积盘粒子"""
        if current_time is None:
            current_time = time.time()

        # 吸积盘参数
        inner_radius = self.isco_radius * 1.2  # 略大于ISCO
        outer_radius = 15.0 * self.radius

        # 确定粒子在吸积盘中的径向位置
        radius_factor = np.random.random() ** 0.5  # 非线性分布
        distance = inner_radius + (outer_radius - inner_radius) * radius_factor

        # 确定角度 - 均匀分布但添加一些变化
        base_angle = (particle['id'] * 0.1 + current_time * 0.1) % (2.0 * math.pi)
        angle_variation = 0.2 * (np.random.random() - 0.5)
        angle = base_angle + angle_variation

        # 计算吸积盘厚度
        base_thickness = 0.5 * (1.0 - 0.5 * self.spin_parameter)
        thickness_factor = 0.5 + 0.5 * (distance - inner_radius) / (outer_radius - inner_radius)
        thickness = base_thickness * thickness_factor * self.radius

        # 计算z坐标
        z_offset = np.random.normal(0, thickness * 0.3)

        # 计算位置
        particle['position'][0] = self.position[0] + distance * math.cos(angle)
        particle['position'][1] = self.position[1] + distance * math.sin(angle)
        particle['position'][2] = self.position[2] + z_offset

        # 计算轨道速度
        orbital_speed = math.sqrt(G * self.mass / distance) * SCALE_FACTOR * 5.0

        # 自旋效应
        if distance < self.radius * 8.0:
            frame_dragging = self.spin_parameter * (1.0 - distance / (self.radius * 8.0)) * 0.3
            orbital_speed *= (1.0 + frame_dragging)
            vz = orbital_speed * 0.05 * frame_dragging * (np.random.random() - 0.5)
        else:
            vz = 0.0

        # 基本轨道速度
        particle['velocity'][0] = -orbital_speed * math.sin(angle)
        particle['velocity'][1] = orbital_speed * math.cos(angle)
        particle['velocity'][2] = vz

        # 计算温度
        max_temp = 10000  # K
        temperature = max_temp * (inner_radius / distance) ** (3.0/4.0)

        # 随机选择粒子类型
        particle_type = np.random.choice(['normal', 'hot_spot', 'dust'], p=[0.8, 0.15, 0.05])

        # 根据粒子类型设置属性
        if particle_type == 'hot_spot':
            # 热点
            r, g, b = self.temperature_to_rgb(temperature * 1.2)  # 热点温度更高
            size = 3.0 + np.random.random() * 2.0
            lifetime = 0.0
            max_lifetime = 2.0 + np.random.random() * 3.0
        elif particle_type == 'dust':
            # 尘埃
            r, g, b = self.temperature_to_rgb(temperature * 0.8)  # 尘埃温度更低
            r = r * 0.8
            g = g * 0.6
            b = b * 0.4
            size = 1.0 + np.random.random()
            lifetime = 0.0
            max_lifetime = 10.0 + np.random.random() * 5.0
        else:
            # 普通粒子
            r, g, b = self.temperature_to_rgb(temperature)
            size = 2.0 + np.random.random()
            lifetime = 0.0
            max_lifetime = 5.0 + np.random.random() * 5.0

        # 添加一些随机变化
        r = min(1.0, r * (0.9 + 0.2 * np.random.random()))
        g = min(1.0, g * (0.9 + 0.2 * np.random.random()))
        b = min(1.0, b * (0.9 + 0.2 * np.random.random()))

        # 更新粒子属性
        particle['distance'] = distance
        particle['angle'] = angle
        particle['color'] = (r, g, b)
        particle['size'] = size
        particle['type'] = particle_type
        particle['temperature'] = temperature
        particle['lifetime'] = lifetime
        particle['max_lifetime'] = max_lifetime
        particle['trail'] = []  # 清除轨迹

    def temperature_to_rgb(self, temperature):
        """将温度转换为RGB颜色 (基于黑体辐射)"""
        # 温度范围
        min_temp = 1000  # K
        max_temp = 10000  # K

        # 将温度限制在有效范围内
        temp = max(min_temp, min(max_temp, temperature))

        # 归一化温度 (0-1)
        t = (temp - min_temp) / (max_temp - min_temp)

        # 基于温度计算RGB
        # 低温 -> 红色
        # 中温 -> 黄色/白色
        # 高温 -> 蓝白色
        if t < 0.33:  # 低温
            r = 1.0
            g = t * 3.0
            b = 0.0
        elif t < 0.66:  # 中温
            r = 1.0
            g = 1.0
            b = (t - 0.33) * 3.0
        else:  # 高温
            r = 1.0
            g = 1.0
            b = 1.0
            # 最高温度时偏蓝
            if t > 0.9:
                r = max(0.0, r - (t - 0.9) * 5.0)
                g = max(0.0, g - (t - 0.9) * 5.0)

        return r, g, b

    def update_jet_particles(self, dt):
        """更新喷流粒子"""
        # 使用CUDA加速粒子更新
        if CUDA_AVAILABLE:
            self._update_jet_particles_cuda(dt)
        else:
            self._update_jet_particles_cpu(dt)

    def _update_jet_particles_cuda(self, dt):
        """使用CUDA加速更新喷流粒子"""
        try:
            from numba import cuda
            import numpy as np

            # 更新所有粒子
            for particle in self.jet_particles:
                # 更新位置
                particle['position'][0] += particle['velocity'][0] * dt
                particle['position'][1] += particle['velocity'][1] * dt
                particle['position'][2] += particle['velocity'][2] * dt

                # 更新轨迹
                if len(particle['trail']) > 20:
                    particle['trail'].pop(0)
                particle['trail'].append(particle['position'].copy())

                # 如果粒子太远，重置到喷流基部
                dz = abs(particle['position'][2] - self.position[2])
                if dz > self.radius * 15.0:
                    direction = particle['direction']
                    distance = 2.0  # 初始距离

                    # 在轴附近生成新位置
                    angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                    radius = 0.5 * (particle['id'] % 10) / 10.0

                    particle['position'][0] = self.position[0] + radius * math.cos(angle)
                    particle['position'][1] = self.position[1] + radius * math.sin(angle)
                    particle['position'][2] = self.position[2] + direction * distance

                    # 新速度 - 更快的喷流速度
                    speed = (0.15 + (particle['id'] % 5) * 0.12) * C * SCALE_FACTOR * 0.01
                    particle['velocity'][0] = 0.0
                    particle['velocity'][1] = 0.0
                    particle['velocity'][2] = direction * speed

                    # 清除轨迹
                    particle['trail'] = []
        except Exception as e:
            print(f"CUDA加速失败，降级为CPU计算: {e}")
            self._update_jet_particles_cpu(dt)

    def _update_jet_particles_cpu(self, dt):
        """使用CPU更新喷流粒子"""
        for particle in self.jet_particles:
            # 获取当前位置相对于黑洞的距离
            dx = particle['position'][0] - self.position[0]
            dy = particle['position'][1] - self.position[1]
            dz = particle['position'][2] - self.position[2]
            distance_from_axis = math.sqrt(dx*dx + dy*dy)  # 距离喷流轴的距离
            distance_from_bh = math.sqrt(dx*dx + dy*dy + dz*dz)  # 距离黑洞的总距离

            # 喷流方向
            direction = particle['direction']

            # 喷流加速效应 - 随着距离黑洞越远，粒子速度越快（基于相对论喷流加速理论）
            # 但在一定距离后会趋于稳定
            if abs(dz) < self.radius * 10.0:
                # 加速区域
                acceleration_factor = min(1.5, 0.5 + abs(dz) / (self.radius * 10.0))

                # 更新z方向速度 - 加速
                current_speed = abs(particle['velocity'][2])
                max_speed = 0.3 * C * SCALE_FACTOR * 0.01 * self.spin_parameter  # 最大速度与自旋参数成正比
                target_speed = max_speed * acceleration_factor

                # 平滑加速
                if current_speed < target_speed:
                    particle['velocity'][2] = direction * (current_speed + (target_speed - current_speed) * 0.1)

                # 喷流束缚效应 - 随着距离增加，喷流会略微发散
                if distance_from_axis > 0.01:
                    # 向轴心方向的力
                    confinement_force = 0.2 * (1.0 - abs(dz) / (self.radius * 10.0))
                    angle_to_axis = math.atan2(dy, dx)

                    # 添加向轴心的速度分量
                    particle['velocity'][0] -= confinement_force * math.cos(angle_to_axis) * dt
                    particle['velocity'][1] -= confinement_force * math.sin(angle_to_axis) * dt

            # 添加一些随机扰动，使喷流看起来更自然
            turbulence = 0.01 * self.spin_parameter
            particle['velocity'][0] += (np.random.random() - 0.5) * turbulence
            particle['velocity'][1] += (np.random.random() - 0.5) * turbulence

            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹 - 增加轨迹长度，使喷流更明显
            if len(particle['trail']) > 30:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 根据距离更新粒子颜色 - 模拟喷流冷却效应
            if direction > 0:  # 蓝色喷流
                cooling_factor = min(1.0, distance_from_bh / (self.radius * 20.0))
                r = max(0.0, 0.2 * (1.0 - cooling_factor))
                g = max(0.2, 0.4 + 0.4 * cooling_factor)
                b = max(0.5, 0.7 + 0.3 * cooling_factor)
            else:  # 红色喷流
                cooling_factor = min(1.0, distance_from_bh / (self.radius * 20.0))
                r = max(0.5, 0.7 + 0.3 * (1.0 - cooling_factor))
                g = max(0.2, 0.4 * cooling_factor)
                b = max(0.0, 0.2 * (1.0 - cooling_factor))

            particle['color'] = (r, g, b)

            # 根据距离更新粒子大小 - 远离黑洞的粒子会膨胀
            expansion_factor = min(2.0, 1.0 + distance_from_bh / (self.radius * 30.0))
            particle['size'] = 1.5 * expansion_factor + 0.5 * np.random.random()

            # 如果粒子太远，重置到喷流基部
            if abs(dz) > self.radius * 25.0:
                distance = 2.0  # 初始距离

                # 在轴附近生成新位置 - 使喷流基部更集中
                angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                radius = 0.3 * (particle['id'] % 10) / 10.0  # 减小初始半径，使喷流更集中

                particle['position'][0] = self.position[0] + radius * math.cos(angle)
                particle['position'][1] = self.position[1] + radius * math.sin(angle)
                particle['position'][2] = self.position[2] + direction * distance

                # 新速度 - 基于自旋参数
                base_speed = 0.1 * C * SCALE_FACTOR * 0.01
                speed_variation = 0.05 * C * SCALE_FACTOR * 0.01 * (particle['id'] % 5) / 5.0
                speed = (base_speed + speed_variation) * self.spin_parameter

                # 添加小的横向速度分量，使喷流有初始发散
                particle['velocity'][0] = (np.random.random() - 0.5) * speed * 0.05
                particle['velocity'][1] = (np.random.random() - 0.5) * speed * 0.05
                particle['velocity'][2] = direction * speed

                # 清除轨迹
                particle['trail'] = []

# 黑洞类 (保留原有实现，继承自克尔黑洞)
class BlackHole(KerrBlackHole):
    """黑洞类 (克尔黑洞的别名，用于向后兼容)"""

    def __init__(self, x=0, y=0, z=0, mass=1e6):
        """初始化黑洞，使用默认的高自旋参数"""
        super().__init__(x, y, z, mass, 0.998, "克尔黑洞")

    # 显式实现抽象方法，调用父类的实现
    def update(self, dt):
        """更新黑洞物理"""
        super().update(dt)

    def render(self):
        """渲染黑洞"""
        # 保存当前矩阵
        glPushMatrix()

        # 移动到黑洞位置
        glTranslatef(*self.position)

        # 渲染爱因斯坦环 - 克尔黑洞的爱因斯坦环会随自旋变形
        if self.einstein_ring_enabled:
            self.render_einstein_ring_kerr()

        # 渲染事件视界辉光 - 克尔黑洞的辉光会随自旋变形
        if self.event_horizon_glow:
            self.render_event_horizon_glow_kerr()

        # 增强旋转效果 - 基于自旋参数，使旋转更加明显
        if hasattr(self, 'rotation_angle'):
            # 大幅增加旋转速度，使旋转效果更加明显
            rotation_speed = 2.0 * self.spin_parameter  # 增加旋转速度
            self.rotation_angle += rotation_speed
            if self.rotation_angle > 360:
                self.rotation_angle -= 360
        else:
            self.rotation_angle = 0

        # 增强旋转可视化 - 添加多轴旋转效果
        glRotatef(self.rotation_angle, 0, 0, 1)  # 主旋转轴

        # 添加进动效果 - 使黑洞看起来更动态
        if self.spin_parameter > 0.5:
            precession_angle = self.rotation_angle * 0.1 * self.spin_parameter
            glRotatef(precession_angle, 1, 0, 0)  # 轻微的进动

        # 渲染事件视界 (黑色球体) - 克尔黑洞的事件视界是扁平的
        glColor4f(0.0, 0.0, 0.0, 1.0)
        # 保存当前矩阵
        glPushMatrix()
        # 应用扁平化 - 基于自旋参数
        flattening = 0.3 * self.spin_parameter
        glScalef(1.0, 1.0, 1.0 - flattening)
        gluSphere(self.quadric, self.radius, 32, 32)
        # 恢复矩阵
        glPopMatrix()

        # 渲染光子球 (半透明蓝色球体) - 克尔黑洞的光子球也是扁平的
        glColor4f(0.0, 0.3, 0.8, 0.2)
        # 保存当前矩阵
        glPushMatrix()
        # 应用扁平化 - 基于自旋参数
        glScalef(1.0, 1.0, 1.0 - flattening)
        gluSphere(self.quadric, self.photon_sphere_radius, 32, 32)
        # 恢复矩阵
        glPopMatrix()

        # 渲染自旋指示器 - 使黑洞旋转更加可视化
        self.render_spin_indicators()

        # 恢复矩阵状态（为了让吸积盘和喷流有自己的变换）
        glPopMatrix()
        glPushMatrix()

        # 移动到黑洞位置
        glTranslatef(*self.position)

        # 渲染吸积盘粒子
        self.render_particles(self.disk_particles)

        # 渲染喷流粒子
        if self.spin_parameter > 0.6:
            self.render_particles(self.jet_particles)

        # 如果启用了引力波，渲染引力波效果
        if self.gravitational_waves_enabled and self.wave_amplitude > 0:
            self.render_gravitational_waves()

        # 渲染时空扭曲效果 - 使框拖效应更加可视化
        self.render_frame_dragging_effect()

        # 恢复矩阵
        glPopMatrix()

    def render_spin_indicators(self):
        """渲染增强的自旋指示器，使黑洞旋转更加震撼可视化"""
        # 只有当自旋参数足够大时才渲染
        if self.spin_parameter < 0.1:
            return

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 获取当前时间用于动态效果
        current_time = time.time()

        # 渲染多层自旋指示器
        num_layers = 3
        for layer in range(num_layers):
            # 每层的半径
            layer_radius = self.radius * (1.3 + 0.4 * layer)

            # 每层的指示器数量
            num_indicators = int(12 * self.spin_parameter * (1.0 + 0.5 * layer))

            for i in range(num_indicators):
                # 计算指示器位置，添加时间偏移使其旋转
                base_angle = 2.0 * math.pi * i / num_indicators
                time_offset = current_time * self.spin_parameter * (1.0 + layer * 0.5)
                angle = base_angle + time_offset

                # 指示器起点
                start_x = layer_radius * math.cos(angle)
                start_y = layer_radius * math.sin(angle)
                start_z = (layer - 1) * 0.2 * self.radius  # 不同层有不同高度

                # 指示器终点 - 沿切线方向，长度基于自旋参数
                arrow_length = 0.8 * self.radius * self.spin_parameter
                end_x = start_x + arrow_length * math.cos(angle + math.pi/2)
                end_y = start_y + arrow_length * math.sin(angle + math.pi/2)
                end_z = start_z

                # 指示器颜色 - 基于自旋参数和层数
                if layer == 0:  # 内层 - 蓝色
                    r, g, b = 0.0, 0.3 + 0.7 * self.spin_parameter, 1.0
                elif layer == 1:  # 中层 - 青色
                    r, g, b = 0.0, 0.8 + 0.2 * self.spin_parameter, 0.8
                else:  # 外层 - 紫色
                    r, g, b = 0.5 + 0.5 * self.spin_parameter, 0.2, 1.0

                alpha = 0.8 * self.spin_parameter * (1.0 - layer * 0.2)

                # 渲染增强的指示器线段
                glLineWidth(3.0 + layer)
                glBegin(GL_LINES)
                glColor4f(r, g, b, alpha)
                glVertex3f(start_x, start_y, start_z)
                glColor4f(r, g, b, 0.0)  # 渐变到透明
                glVertex3f(end_x, end_y, end_z)
                glEnd()

                # 渲染增强的箭头尖端
                glPointSize(6.0 + layer * 2.0)
                glBegin(GL_POINTS)
                glColor4f(r, g, b, alpha)
                glVertex3f(end_x, end_y, end_z)
                glEnd()

                # 渲染箭头侧翼，使箭头更明显
                wing_length = arrow_length * 0.3
                wing_angle1 = angle + math.pi/2 - math.pi/6
                wing_angle2 = angle + math.pi/2 + math.pi/6

                wing1_x = start_x + wing_length * math.cos(wing_angle1)
                wing1_y = start_y + wing_length * math.sin(wing_angle1)
                wing2_x = start_x + wing_length * math.cos(wing_angle2)
                wing2_y = start_y + wing_length * math.sin(wing_angle2)

                glLineWidth(2.0)
                glBegin(GL_LINES)
                # 左翼
                glColor4f(r, g, b, alpha * 0.7)
                glVertex3f(end_x, end_y, end_z)
                glColor4f(r, g, b, 0.0)
                glVertex3f(wing1_x, wing1_y, start_z)
                # 右翼
                glColor4f(r, g, b, alpha * 0.7)
                glVertex3f(end_x, end_y, end_z)
                glColor4f(r, g, b, 0.0)
                glVertex3f(wing2_x, wing2_y, start_z)
                glEnd()

        # 渲染中心旋转涡流效果
        self.render_central_vortex(current_time)

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_central_vortex(self, current_time):
        """渲染中心旋转涡流效果"""
        if self.spin_parameter < 0.3:
            return

        # 涡流半径
        vortex_radius = self.radius * 0.8

        # 涡流圈数
        num_spirals = int(6 * self.spin_parameter)

        for spiral in range(num_spirals):
            # 螺旋起始角度
            start_angle = 2.0 * math.pi * spiral / num_spirals + current_time * self.spin_parameter * 3.0

            # 渲染螺旋
            glLineWidth(2.0)
            glBegin(GL_LINE_STRIP)

            segments = 32
            for i in range(segments):
                t = i / (segments - 1)
                angle = start_angle + t * 4.0 * math.pi * self.spin_parameter
                radius = vortex_radius * (1.0 - t * 0.8)  # 向中心收缩

                x = radius * math.cos(angle)
                y = radius * math.sin(angle)
                z = 0.0

                # 颜色从外到内变化
                r = 0.2 + 0.8 * t
                g = 0.1 + 0.6 * self.spin_parameter * (1.0 - t)
                b = 0.9
                alpha = (1.0 - t) * 0.6 * self.spin_parameter

                glColor4f(r, g, b, alpha)
                glVertex3f(x, y, z)

            glEnd()

    def render_frame_dragging_effect(self):
        """渲染增强的框拖效应，使时空扭曲更加震撼可视化"""
        # 只有当自旋参数足够大时才渲染
        if self.spin_parameter < 0.3:
            return

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 当前时间
        current_time = time.time()

        # 渲染多层时空扭曲效果
        num_layers = 4
        for layer in range(num_layers):
            # 每层的螺旋线数量
            num_spirals = int(8 * self.spin_parameter * (1.0 + layer * 0.3))

            # 每层的基础半径
            base_radius = self.radius * (2.0 + layer * 1.5)

            # 每层的旋转速度
            rotation_speed = self.spin_parameter * (1.0 + layer * 0.2)

            for i in range(num_spirals):
                # 螺旋线起始角度，每层有不同的时间偏移
                start_angle = (2.0 * math.pi * i / num_spirals +
                              current_time * rotation_speed +
                              layer * math.pi / 4)

                # 渲染增强的螺旋线
                glLineWidth(2.0 + layer * 0.5)
                glBegin(GL_LINE_STRIP)

                # 螺旋线段数
                segments = 96
                for j in range(segments):
                    # 螺旋线参数
                    t = j / (segments - 1)  # 参数 t 从 0 到 1

                    # 螺旋角度 - 增加扭曲程度
                    spiral_turns = 6.0 * self.spin_parameter * (1.0 + layer * 0.5)
                    angle = start_angle + t * spiral_turns * math.pi

                    # 螺旋半径 - 从内到外扩展
                    radius = base_radius * (0.3 + 0.7 * t)

                    # 添加波动效果
                    wave_amplitude = 0.2 * self.spin_parameter * radius
                    wave_frequency = 8.0 + layer * 2.0
                    radius += wave_amplitude * math.sin(t * wave_frequency + current_time * 2.0)

                    # 计算位置
                    x = radius * math.cos(angle)
                    y = radius * math.sin(angle)

                    # 添加z方向的扭曲
                    z_amplitude = 0.3 * self.spin_parameter * radius * (1.0 - t)
                    z = z_amplitude * math.sin(angle * 2.0 + current_time)

                    # 计算颜色 - 基于层数和位置
                    if layer == 0:  # 内层 - 深蓝色
                        r, g, b = 0.0, 0.2 + 0.3 * self.spin_parameter, 0.8 + 0.2 * self.spin_parameter
                    elif layer == 1:  # 第二层 - 青色
                        r, g, b = 0.0, 0.5 + 0.3 * self.spin_parameter, 0.7
                    elif layer == 2:  # 第三层 - 紫色
                        r, g, b = 0.3 + 0.4 * self.spin_parameter, 0.2, 0.8
                    else:  # 外层 - 粉色
                        r, g, b = 0.6 + 0.4 * self.spin_parameter, 0.1, 0.6

                    # 透明度从内到外递减
                    alpha = (1.0 - t) * 0.6 * self.spin_parameter * (1.0 - layer * 0.15)

                    # 添加脉动效果
                    pulse = 0.3 * math.sin(current_time * 3.0 + angle + layer * math.pi)
                    alpha *= (1.0 + pulse)

                    glColor4f(r, g, b, max(0.0, alpha))
                    glVertex3f(x, y, z)

                glEnd()

        # 渲染时空网格扭曲效果
        self.render_spacetime_grid_distortion(current_time)

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_spacetime_grid_distortion(self, current_time):
        """渲染时空网格扭曲效果"""
        if self.spin_parameter < 0.5:
            return

        # 网格参数
        grid_radius = self.radius * 6.0
        grid_lines = int(16 * self.spin_parameter)

        # 渲染径向网格线
        for i in range(grid_lines):
            angle = 2.0 * math.pi * i / grid_lines

            glLineWidth(1.0)
            glBegin(GL_LINE_STRIP)

            segments = 32
            for j in range(segments):
                t = j / (segments - 1)
                radius = self.radius * 2.0 + t * (grid_radius - self.radius * 2.0)

                # 应用框拖效应扭曲
                distortion_angle = self.spin_parameter * (1.0 - t) * math.sin(current_time + angle)
                distorted_angle = angle + distortion_angle * 0.5

                x = radius * math.cos(distorted_angle)
                y = radius * math.sin(distorted_angle)
                z = 0.0

                # 颜色基于扭曲程度
                distortion_amount = abs(distortion_angle)
                r = 0.2 + 0.8 * distortion_amount
                g = 0.3 + 0.4 * self.spin_parameter
                b = 0.8
                alpha = (1.0 - t) * 0.3 * self.spin_parameter

                glColor4f(r, g, b, alpha)
                glVertex3f(x, y, z)

            glEnd()

        # 渲染环形网格线
        num_rings = int(8 * self.spin_parameter)
        for i in range(num_rings):
            t = i / (num_rings - 1)
            radius = self.radius * 2.0 + t * (grid_radius - self.radius * 2.0)

            glLineWidth(1.0)
            glBegin(GL_LINE_STRIP)

            segments = 64
            for j in range(segments + 1):
                angle = 2.0 * math.pi * j / segments

                # 应用框拖效应扭曲
                distortion = self.spin_parameter * (1.0 - t) * 0.2 * math.sin(angle * 4.0 + current_time * 2.0)
                distorted_radius = radius * (1.0 + distortion)

                x = distorted_radius * math.cos(angle)
                y = distorted_radius * math.sin(angle)
                z = 0.0

                # 颜色
                r = 0.2
                g = 0.3 + 0.4 * self.spin_parameter
                b = 0.8
                alpha = (1.0 - t) * 0.3 * self.spin_parameter

                glColor4f(r, g, b, alpha)
                glVertex3f(x, y, z)

            glEnd()

    def render_einstein_ring_kerr(self):
        """渲染克尔黑洞的爱因斯坦环（考虑自旋效应）"""
        # 如果爱因斯坦环半径未计算，则计算它
        if self.einstein_ring_radius <= 0:
            self.calculate_einstein_ring()

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试，以便环始终可见
        glDisable(GL_DEPTH_TEST)

        # 环的颜色 - 蓝白色
        glColor4f(0.7, 0.8, 1.0, 0.3)

        # 渲染环 - 考虑自旋效应，使环变形
        glBegin(GL_LINE_LOOP)
        segments = 64
        for i in range(segments):
            angle = 2.0 * math.pi * i / segments

            # 基础半径
            radius = self.einstein_ring_radius

            # 应用自旋效应 - 使环在自旋方向上变形
            # 自旋方向上的环会被拉伸
            spin_effect = 0.2 * self.spin_parameter * math.sin(angle)
            radius *= (1.0 + spin_effect)

            # 计算位置
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)

            # 添加z方向的变形 - 使环不完全平面
            z_distortion = 0.1 * self.spin_parameter * math.sin(angle * 2.0)
            z = z_distortion * radius

            glVertex3f(x, y, z)
        glEnd()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_event_horizon_glow_kerr(self):
        """渲染克尔黑洞的事件视界辉光（考虑自旋效应）"""
        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 渲染多层辉光
        num_layers = 5
        for i in range(num_layers):
            # 辉光大小
            glow_size = self.radius * (1.02 + 0.02 * i)

            # 辉光透明度
            alpha = 0.1 * (1.0 - i / num_layers)

            # 辉光颜色 - 基于自旋参数调整
            # 高自旋黑洞的辉光更蓝，低自旋黑洞的辉光更紫
            r = self.horizon_glow_color[0] * (1.0 - 0.5 * self.spin_parameter)
            g = self.horizon_glow_color[1]
            b = self.horizon_glow_color[2] * (0.5 + 0.5 * self.spin_parameter)

            # 保存当前矩阵
            glPushMatrix()

            # 应用扁平化 - 基于自旋参数
            flattening = 0.3 * self.spin_parameter
            glScalef(1.0, 1.0, 1.0 - flattening)

            # 渲染辉光
            glColor4f(r, g, b, alpha)
            gluSphere(self.quadric, glow_size, 32, 32)

            # 恢复矩阵
            glPopMatrix()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

# 中子星类
class NeutronStar(CelestialBody):
    """中子星类"""

    def __init__(self, x=0, y=0, z=0, mass=2.0*M_SUN, radius=10000, name="中子星"):
        super().__init__(x, y, z, mass, name)
        self.type = CelestialType.NEUTRON_STAR

        # 中子星参数
        self.physical_radius = radius  # 物理半径 (m)
        self.radius = max(1.0, self.physical_radius * SCALE_FACTOR)  # 可视半径

        # 中子星特有参数
        self.rotation_period = 0.01  # 自转周期 (s)
        self.magnetic_field = 1e8  # 磁场强度 (T)
        self.surface_temperature = 1e6  # 表面温度 (K)

        # 粒子系统
        self.particles = []
        self.generate_particles(500 if CUDA_AVAILABLE and RTX_ENABLED else 200)

    def generate_particles(self, num_particles):
        """生成中子星粒子 (模拟磁场和辐射)"""
        for i in range(num_particles):
            # 随机角度
            theta = np.random.random() * math.pi
            phi = np.random.random() * 2.0 * math.pi

            # 随机距离 (从表面开始)
            distance = self.radius * (1.0 + 0.5 * np.random.random())

            # 计算位置 (球坐标转笛卡尔坐标)
            x = self.position[0] + distance * math.sin(theta) * math.cos(phi)
            y = self.position[1] + distance * math.sin(theta) * math.sin(phi)
            z = self.position[2] + distance * math.cos(theta)

            # 计算速度 (沿磁力线)
            speed = 0.1 * C * SCALE_FACTOR * (0.5 + 0.5 * np.random.random())
            vx = speed * math.sin(theta) * math.cos(phi)
            vy = speed * math.sin(theta) * math.sin(phi)
            vz = speed * math.cos(theta)

            # 粒子颜色 (基于温度 - 蓝白色)
            r = 0.7 + 0.3 * np.random.random()
            g = 0.8 + 0.2 * np.random.random()
            b = 1.0

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': 1.0 + np.random.random(),
                'lifetime': 0.0,
                'max_lifetime': 1.0 + np.random.random(),
                'trail': []
            }

            self.particles.append(particle)

    def update_particles(self, dt):
        """更新中子星粒子"""
        rotation_angle = dt * 2.0 * math.pi / self.rotation_period

        for particle in self.particles:
            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹
            if len(particle['trail']) > 10:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 更新生命周期
            particle['lifetime'] += dt

            # 如果粒子生命周期结束，重新生成
            if particle['lifetime'] > particle['max_lifetime']:
                # 随机角度 (考虑自转)
                theta = np.random.random() * math.pi
                phi = np.random.random() * 2.0 * math.pi + rotation_angle

                # 随机距离 (从表面开始)
                distance = self.radius * (1.0 + 0.1 * np.random.random())

                # 计算新位置
                particle['position'][0] = self.position[0] + distance * math.sin(theta) * math.cos(phi)
                particle['position'][1] = self.position[1] + distance * math.sin(theta) * math.sin(phi)
                particle['position'][2] = self.position[2] + distance * math.cos(theta)

                # 计算新速度
                speed = 0.1 * C * SCALE_FACTOR * (0.5 + 0.5 * np.random.random())
                particle['velocity'][0] = speed * math.sin(theta) * math.cos(phi)
                particle['velocity'][1] = speed * math.sin(theta) * math.sin(phi)
                particle['velocity'][2] = speed * math.cos(theta)

                # 重置生命周期
                particle['lifetime'] = 0.0
                particle['max_lifetime'] = 1.0 + np.random.random()

                # 清除轨迹
                particle['trail'] = []

    def update(self, dt):
        """更新中子星物理"""
        self.update_particles(dt)

    def render(self):
        """渲染中子星"""
        # 保存当前矩阵
        glPushMatrix()

        # 移动到中子星位置
        glTranslatef(*self.position)

        # 渲染中子星表面 (蓝白色球体)
        glColor4f(0.7, 0.8, 1.0, 1.0)
        gluSphere(self.quadric, self.radius, 32, 32)

        # 渲染粒子
        self.render_particles()

        # 恢复矩阵
        glPopMatrix()

    def render_particles(self):
        """渲染中子星粒子"""
        # 启用点平滑
        glEnable(GL_POINT_SMOOTH)
        glHint(GL_POINT_SMOOTH_HINT, GL_NICEST)

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 渲染粒子
        for particle in self.particles:
            glPointSize(particle['size'])
            glColor4f(*particle['color'], 1.0)
            glBegin(GL_POINTS)
            glVertex3f(*particle['position'])
            glEnd()

        # 渲染粒子轨迹
        glBegin(GL_LINES)
        for particle in self.particles:
            if len(particle['trail']) > 1:
                for i in range(len(particle['trail']) - 1):
                    # 轨迹颜色渐变
                    alpha = i / len(particle['trail'])
                    glColor4f(*particle['color'], alpha * 0.5)
                    glVertex3f(*particle['trail'][i])
                    glVertex3f(*particle['trail'][i+1])
        glEnd()

        # 禁用混合
        glDisable(GL_BLEND)
        glDisable(GL_POINT_SMOOTH)

# 恒星类
class Star(CelestialBody):
    """恒星基类"""

    def __init__(self, x=0, y=0, z=0, mass=1.0*M_SUN, radius=1.0*R_SUN,
                 temperature=5800, name="恒星"):
        super().__init__(x, y, z, mass, name)

        # 恒星参数
        self.physical_radius = radius  # 物理半径 (m)
        self.radius = max(3.0, self.physical_radius * SCALE_FACTOR)  # 可视半径
        self.temperature = temperature  # 表面温度 (K)

        # 计算颜色 (基于温度)
        self.color = self.temperature_to_rgb(temperature)

        # 粒子系统 (恒星大气和日冕)
        self.corona_particles = []
        self.generate_corona(300 if CUDA_AVAILABLE and RTX_ENABLED else 150)

    def temperature_to_rgb(self, temp):
        """将温度转换为RGB颜色 (黑体辐射近似)"""
        # 温度范围: 1000K (红) 到 40000K (蓝)
        temp = max(1000, min(40000, temp))

        # 红色分量
        if temp < 6600:
            r = 1.0
        else:
            r = min(1.0, (temp / 100 - 60) * -0.0029 + 1.1)

        # 绿色分量
        if temp < 6600:
            g = min(1.0, 0.39 * math.log(temp / 100) - 0.634)
        else:
            g = min(1.0, (temp / 100 - 60) * -0.0024 + 0.9)

        # 蓝色分量
        if temp < 2000:
            b = 0.0
        elif temp < 6600:
            b = min(1.0, 0.76 * math.log(temp / 100 - 10) - 1.5)
        else:
            b = 1.0

        return (r, g, b)

    def generate_corona(self, num_particles):
        """生成恒星大气和日冕粒子"""
        # 增加粒子数量，使日冕更加丰富
        for i in range(num_particles):
            # 确定粒子类型 - 日冕环、日冕物质抛射或普通日冕粒子
            particle_type = np.random.choice(['loop', 'cme', 'normal'], p=[0.3, 0.1, 0.6])

            if particle_type == 'loop':
                # 日冕环 - 沿磁力线的弧形结构
                # 随机选择一个基点角度
                base_theta = np.random.random() * math.pi
                base_phi = np.random.random() * 2.0 * math.pi

                # 环的大小和方向
                loop_size = self.radius * (0.5 + 1.0 * np.random.random())
                loop_direction = np.random.random() * 2.0 * math.pi

                # 计算基点位置
                base_x = self.position[0] + self.radius * math.sin(base_theta) * math.cos(base_phi)
                base_y = self.position[1] + self.radius * math.sin(base_theta) * math.sin(base_phi)
                base_z = self.position[2] + self.radius * math.cos(base_theta)

                # 计算位置 - 在基点上方形成弧形
                arc_height = loop_size * (0.5 + 0.5 * np.random.random())
                distance = self.radius + arc_height * np.random.random()

                x = base_x + arc_height * 0.5 * math.sin(loop_direction)
                y = base_y + arc_height * 0.5 * math.cos(loop_direction)
                z = base_z + arc_height * 0.5

                # 计算速度 - 沿磁力线方向
                speed = 0.03 * C * SCALE_FACTOR * np.random.random()
                direction_vector = [x - base_x, y - base_y, z - base_z]
                magnitude = math.sqrt(sum(d*d for d in direction_vector))
                if magnitude > 0:
                    vx = speed * direction_vector[0] / magnitude
                    vy = speed * direction_vector[1] / magnitude
                    vz = speed * direction_vector[2] / magnitude
                else:
                    vx, vy, vz = 0, 0, 0

                # 粒子颜色 - 日冕环通常更亮
                temp_factor = 1.2  # 温度因子，日冕环通常更热
                r = min(1.0, self.color[0] * temp_factor)
                g = min(1.0, self.color[1] * temp_factor)
                b = min(1.0, self.color[2] * temp_factor)

                # 粒子大小 - 日冕环粒子通常更大
                size = 2.0 + np.random.random()

                # 生命周期 - 日冕环持续时间较长
                max_lifetime = 4.0 + 2.0 * np.random.random()

            elif particle_type == 'cme':
                # 日冕物质抛射 - 大规模、高速的物质喷发
                # 随机角度，但集中在某个区域
                cme_center_theta = np.random.random() * math.pi
                cme_center_phi = np.random.random() * 2.0 * math.pi
                spread = 0.3  # 扩散范围

                theta = cme_center_theta + (np.random.random() - 0.5) * spread
                phi = cme_center_phi + (np.random.random() - 0.5) * spread

                # 随机距离 - CME从表面开始，但可以延伸很远
                distance = self.radius * (1.0 + 2.0 * np.random.random())

                # 计算位置
                x = self.position[0] + distance * math.sin(theta) * math.cos(phi)
                y = self.position[1] + distance * math.sin(theta) * math.sin(phi)
                z = self.position[2] + distance * math.cos(theta)

                # 计算速度 - CME速度很高
                speed = 0.2 * C * SCALE_FACTOR * (0.8 + 0.4 * np.random.random())
                vx = speed * math.sin(theta) * math.cos(phi)
                vy = speed * math.sin(theta) * math.sin(phi)
                vz = speed * math.cos(theta)

                # 粒子颜色 - CME通常更亮、更热
                r = min(1.0, self.color[0] * 1.5)
                g = min(1.0, self.color[1] * 1.3)
                b = min(1.0, self.color[2] * 1.1)

                # 粒子大小 - CME粒子通常更大
                size = 2.5 + 1.5 * np.random.random()

                # 生命周期 - CME持续时间较短
                max_lifetime = 1.0 + np.random.random()

            else:  # 'normal'
                # 普通日冕粒子
                # 随机角度
                theta = np.random.random() * math.pi
                phi = np.random.random() * 2.0 * math.pi

                # 随机距离 (从表面开始)
                distance = self.radius * (1.0 + 0.8 * np.random.random())

                # 计算位置
                x = self.position[0] + distance * math.sin(theta) * math.cos(phi)
                y = self.position[1] + distance * math.sin(theta) * math.sin(phi)
                z = self.position[2] + distance * math.cos(theta)

                # 计算速度 (向外辐射)
                speed = 0.08 * C * SCALE_FACTOR * np.random.random()
                vx = speed * math.sin(theta) * math.cos(phi)
                vy = speed * math.sin(theta) * math.sin(phi)
                vz = speed * math.cos(theta)

                # 粒子颜色 (基于恒星颜色，略微变化)
                r = min(1.0, self.color[0] + 0.2 * np.random.random())
                g = min(1.0, self.color[1] + 0.2 * np.random.random())
                b = min(1.0, self.color[2] + 0.2 * np.random.random())

                # 普通粒子大小
                size = 1.5 + np.random.random()

                # 普通生命周期
                max_lifetime = 2.0 + np.random.random()

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': size,
                'lifetime': 0.0,
                'max_lifetime': max_lifetime,
                'type': particle_type,
                'trail': []
            }

            self.corona_particles.append(particle)

    def update_corona(self, dt):
        """更新恒星大气和日冕粒子"""
        for particle in self.corona_particles:
            # 根据粒子类型应用不同的更新逻辑
            particle_type = particle.get('type', 'normal')

            if particle_type == 'loop':
                # 日冕环粒子 - 沿磁力线运动
                # 添加一些随机扰动，使日冕环看起来更自然
                turbulence = 0.005
                particle['velocity'][0] += (np.random.random() - 0.5) * turbulence
                particle['velocity'][1] += (np.random.random() - 0.5) * turbulence
                particle['velocity'][2] += (np.random.random() - 0.5) * turbulence

                # 限制速度，使粒子不会飞得太远
                speed = math.sqrt(sum(v*v for v in particle['velocity']))
                if speed > 0.05 * C * SCALE_FACTOR:
                    factor = 0.05 * C * SCALE_FACTOR / speed
                    particle['velocity'][0] *= factor
                    particle['velocity'][1] *= factor
                    particle['velocity'][2] *= factor

            elif particle_type == 'cme':
                # 日冕物质抛射 - 高速向外运动
                # CME粒子加速度随距离增加
                dx = particle['position'][0] - self.position[0]
                dy = particle['position'][1] - self.position[1]
                dz = particle['position'][2] - self.position[2]
                distance = math.sqrt(dx*dx + dy*dy + dz*dz)

                # 如果距离足够远，增加速度
                if distance > self.radius * 2.0:
                    # 计算方向向量
                    direction = [dx/distance, dy/distance, dz/distance]

                    # 增加速度
                    acceleration = 0.1 * C * SCALE_FACTOR * dt
                    particle['velocity'][0] += direction[0] * acceleration
                    particle['velocity'][1] += direction[1] * acceleration
                    particle['velocity'][2] += direction[2] * acceleration

                # 根据距离调整颜色 - 模拟冷却效应
                cooling_factor = min(1.0, distance / (self.radius * 10.0))
                r = max(self.color[0], min(1.0, particle['color'][0] * (1.0 - cooling_factor * 0.3)))
                g = max(self.color[1], min(1.0, particle['color'][1] * (1.0 - cooling_factor * 0.2)))
                b = max(self.color[2], min(1.0, particle['color'][2] * (1.0 - cooling_factor * 0.1)))
                particle['color'] = (r, g, b)

                # 根据距离调整大小 - 模拟膨胀效应
                expansion_factor = min(2.0, 1.0 + distance / (self.radius * 5.0))
                particle['size'] = min(4.0, particle['size'] * (1.0 + 0.05 * expansion_factor))

            else:  # 'normal'
                # 普通日冕粒子 - 添加一些随机扰动
                turbulence = 0.002
                particle['velocity'][0] += (np.random.random() - 0.5) * turbulence
                particle['velocity'][1] += (np.random.random() - 0.5) * turbulence
                particle['velocity'][2] += (np.random.random() - 0.5) * turbulence

            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹 - 增加轨迹长度，使日冕更明显
            trail_length = 10 if particle_type == 'normal' else 20
            if len(particle['trail']) > trail_length:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 更新生命周期
            particle['lifetime'] += dt

            # 如果粒子生命周期结束，重新生成
            if particle['lifetime'] > particle['max_lifetime']:
                # 重新生成一个新的粒子类型
                new_type = np.random.choice(['loop', 'cme', 'normal'], p=[0.3, 0.1, 0.6])

                if new_type == 'loop':
                    # 日冕环 - 沿磁力线的弧形结构
                    base_theta = np.random.random() * math.pi
                    base_phi = np.random.random() * 2.0 * math.pi

                    loop_size = self.radius * (0.5 + 1.0 * np.random.random())
                    loop_direction = np.random.random() * 2.0 * math.pi

                    base_x = self.position[0] + self.radius * math.sin(base_theta) * math.cos(base_phi)
                    base_y = self.position[1] + self.radius * math.sin(base_theta) * math.sin(base_phi)
                    base_z = self.position[2] + self.radius * math.cos(base_theta)

                    arc_height = loop_size * (0.5 + 0.5 * np.random.random())

                    x = base_x + arc_height * 0.5 * math.sin(loop_direction)
                    y = base_y + arc_height * 0.5 * math.cos(loop_direction)
                    z = base_z + arc_height * 0.5

                    speed = 0.03 * C * SCALE_FACTOR * np.random.random()
                    direction_vector = [x - base_x, y - base_y, z - base_z]
                    magnitude = math.sqrt(sum(d*d for d in direction_vector))
                    if magnitude > 0:
                        vx = speed * direction_vector[0] / magnitude
                        vy = speed * direction_vector[1] / magnitude
                        vz = speed * direction_vector[2] / magnitude
                    else:
                        vx, vy, vz = 0, 0, 0

                    temp_factor = 1.2
                    r = min(1.0, self.color[0] * temp_factor)
                    g = min(1.0, self.color[1] * temp_factor)
                    b = min(1.0, self.color[2] * temp_factor)

                    size = 2.0 + np.random.random()
                    max_lifetime = 4.0 + 2.0 * np.random.random()

                elif new_type == 'cme':
                    # 日冕物质抛射
                    cme_center_theta = np.random.random() * math.pi
                    cme_center_phi = np.random.random() * 2.0 * math.pi
                    spread = 0.3

                    theta = cme_center_theta + (np.random.random() - 0.5) * spread
                    phi = cme_center_phi + (np.random.random() - 0.5) * spread

                    distance = self.radius * (1.0 + 0.2 * np.random.random())

                    x = self.position[0] + distance * math.sin(theta) * math.cos(phi)
                    y = self.position[1] + distance * math.sin(theta) * math.sin(phi)
                    z = self.position[2] + distance * math.cos(theta)

                    speed = 0.2 * C * SCALE_FACTOR * (0.8 + 0.4 * np.random.random())
                    vx = speed * math.sin(theta) * math.cos(phi)
                    vy = speed * math.sin(theta) * math.sin(phi)
                    vz = speed * math.cos(theta)

                    r = min(1.0, self.color[0] * 1.5)
                    g = min(1.0, self.color[1] * 1.3)
                    b = min(1.0, self.color[2] * 1.1)

                    size = 2.5 + 1.5 * np.random.random()
                    max_lifetime = 1.0 + np.random.random()

                else:  # 'normal'
                    # 普通日冕粒子
                    theta = np.random.random() * math.pi
                    phi = np.random.random() * 2.0 * math.pi

                    distance = self.radius * (1.0 + 0.1 * np.random.random())

                    x = self.position[0] + distance * math.sin(theta) * math.cos(phi)
                    y = self.position[1] + distance * math.sin(theta) * math.sin(phi)
                    z = self.position[2] + distance * math.cos(theta)

                    speed = 0.08 * C * SCALE_FACTOR * np.random.random()
                    vx = speed * math.sin(theta) * math.cos(phi)
                    vy = speed * math.sin(theta) * math.sin(phi)
                    vz = speed * math.cos(theta)

                    r = min(1.0, self.color[0] + 0.2 * np.random.random())
                    g = min(1.0, self.color[1] + 0.2 * np.random.random())
                    b = min(1.0, self.color[2] + 0.2 * np.random.random())

                    size = 1.5 + np.random.random()
                    max_lifetime = 2.0 + np.random.random()

                # 更新粒子属性
                particle['position'] = [x, y, z]
                particle['velocity'] = [vx, vy, vz]
                particle['color'] = (r, g, b)
                particle['size'] = size
                particle['lifetime'] = 0.0
                particle['max_lifetime'] = max_lifetime
                particle['type'] = new_type

                # 清除轨迹
                particle['trail'] = []

    def update(self, dt):
        """更新恒星物理"""
        self.update_corona(dt)

    def render(self):
        """渲染恒星"""
        # 保存当前矩阵
        glPushMatrix()

        # 移动到恒星位置
        glTranslatef(*self.position)

        # 应用自转效应 - 恒星自转
        if hasattr(self, 'rotation_angle'):
            self.rotation_angle += 0.1  # 自转速度
            if self.rotation_angle > 360:
                self.rotation_angle -= 360
        else:
            self.rotation_angle = 0

        # 应用旋转 - 围绕y轴旋转（恒星自转轴）
        glRotatef(self.rotation_angle, 0, 1, 0)

        # 渲染恒星表面 - 使用更复杂的渲染方法
        self.render_star_surface()

        # 渲染日冕粒子
        self.render_particles()

        # 渲染恒星光晕 - 增强视觉效果
        self.render_star_glow()

        # 恢复矩阵
        glPopMatrix()

    def render_star_surface(self):
        """渲染恒星表面，包括纹理和细节"""
        # 基本颜色
        base_r, base_g, base_b = self.color

        # 渲染基础球体
        glColor4f(base_r, base_g, base_b, 1.0)
        gluSphere(self.quadric, self.radius, 64, 64)  # 增加细分数，使表面更平滑

        # 启用混合，用于渲染表面细节
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

        # 渲染表面纹理 - 颗粒状表面
        self.render_star_granulation()

        # 渲染表面特征 - 恒星黑子
        num_features = int(10 + 20 * np.random.random())

        # 禁用深度测试，以便在球体上绘制
        glDisable(GL_DEPTH_TEST)

        for i in range(num_features):
            # 随机位置
            theta = np.random.random() * math.pi
            phi = np.random.random() * 2.0 * math.pi

            # 计算表面位置
            x = self.radius * 1.001 * math.sin(theta) * math.cos(phi)
            y = self.radius * 1.001 * math.sin(theta) * math.sin(phi)
            z = self.radius * 1.001 * math.cos(theta)

            # 特征大小 - 基于温度调整黑子大小
            # 较冷的恒星有更大的黑子
            temp_factor = min(1.0, self.temperature / 6000)
            feature_size = self.radius * (0.05 + 0.1 * np.random.random()) * (2.0 - temp_factor)

            # 特征颜色 - 黑子为暗色
            darkness = 0.3 + 0.3 * np.random.random()
            feature_r = base_r * darkness
            feature_g = base_g * darkness
            feature_b = base_b * darkness

            # 保存当前矩阵
            glPushMatrix()

            # 移动到特征位置
            glTranslatef(x, y, z)

            # 计算从原点到当前位置的方向
            length = math.sqrt(x*x + y*y + z*z)
            if length > 0:
                # 归一化方向向量
                nx, ny, nz = x/length, y/length, z/length

                # 计算旋转轴和角度
                if nz < 0.99999:  # 避免数值问题
                    # 计算旋转轴 - 叉积(0,0,1)×(nx,ny,nz)
                    axis_x = -ny
                    axis_y = nx
                    axis_z = 0

                    # 计算旋转角度 - 点积(0,0,1)·(nx,ny,nz)
                    angle = math.acos(nz) * 180.0 / math.pi

                    # 应用旋转
                    glRotatef(angle, axis_x, axis_y, axis_z)

            # 渲染特征 - 使用扁平球体表示黑子
            glColor4f(feature_r, feature_g, feature_b, 0.7)
            glScalef(1.0, 1.0, 0.3)  # 使球体扁平，更像真实黑子
            gluSphere(self.quadric, feature_size, 16, 16)

            # 恢复矩阵
            glPopMatrix()

        # 渲染表面特征 - 恒星耀斑
        num_flares = int(5 + 10 * np.random.random())

        # 耀斑数量与温度相关 - 较热的恒星有更多耀斑
        temp_factor = min(1.0, self.temperature / 10000)
        num_flares = int(num_flares * (0.5 + 0.5 * temp_factor))

        for i in range(num_flares):
            # 随机位置
            theta = np.random.random() * math.pi
            phi = np.random.random() * 2.0 * math.pi

            # 计算表面位置
            x = self.radius * 1.001 * math.sin(theta) * math.cos(phi)
            y = self.radius * 1.001 * math.sin(theta) * math.sin(phi)
            z = self.radius * 1.001 * math.cos(theta)

            # 特征大小 - 基于温度调整耀斑大小
            feature_size = self.radius * (0.1 + 0.15 * np.random.random()) * temp_factor

            # 特征颜色 - 耀斑为亮色，颜色基于温度
            brightness = 1.2 + 0.3 * np.random.random()

            # 较热的恒星有更白/蓝的耀斑，较冷的恒星有更红/黄的耀斑
            if self.temperature > 6000:  # 较热的恒星
                feature_r = min(1.0, base_r * brightness * 0.9)
                feature_g = min(1.0, base_g * brightness)
                feature_b = min(1.0, base_b * brightness * 1.1)
            else:  # 较冷的恒星
                feature_r = min(1.0, base_r * brightness * 1.1)
                feature_g = min(1.0, base_g * brightness * 0.9)
                feature_b = min(1.0, base_b * brightness * 0.8)

            # 保存当前矩阵
            glPushMatrix()

            # 移动到特征位置
            glTranslatef(x, y, z)

            # 计算从原点到当前位置的方向
            length = math.sqrt(x*x + y*y + z*z)
            if length > 0:
                # 归一化方向向量
                nx, ny, nz = x/length, y/length, z/length

                # 计算旋转轴和角度
                if nz < 0.99999:  # 避免数值问题
                    # 计算旋转轴 - 叉积(0,0,1)×(nx,ny,nz)
                    axis_x = -ny
                    axis_y = nx
                    axis_z = 0

                    # 计算旋转角度 - 点积(0,0,1)·(nx,ny,nz)
                    angle = math.acos(nz) * 180.0 / math.pi

                    # 应用旋转
                    glRotatef(angle, axis_x, axis_y, axis_z)

            # 渲染特征 - 使用发光效果表示耀斑
            glColor4f(feature_r, feature_g, feature_b, 0.7)

            # 渲染耀斑核心
            gluSphere(self.quadric, feature_size * 0.5, 16, 16)

            # 渲染耀斑光晕
            glColor4f(feature_r, feature_g, feature_b, 0.3)
            gluSphere(self.quadric, feature_size, 16, 16)

            # 恢复矩阵
            glPopMatrix()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_star_granulation(self):
        """渲染恒星表面颗粒状纹理"""
        # 禁用深度测试，以便在球体上绘制
        glDisable(GL_DEPTH_TEST)

        # 颗粒数量 - 基于恒星大小
        num_granules = int(200 * (self.radius / 5.0))

        # 颗粒大小 - 基于恒星温度
        # 较热的恒星有更小的颗粒
        temp_factor = min(1.0, self.temperature / 10000)
        granule_size_factor = 1.0 - 0.5 * temp_factor

        for i in range(num_granules):
            # 随机位置
            theta = np.random.random() * math.pi
            phi = np.random.random() * 2.0 * math.pi

            # 计算表面位置
            x = self.radius * 1.0001 * math.sin(theta) * math.cos(phi)
            y = self.radius * 1.0001 * math.sin(theta) * math.sin(phi)
            z = self.radius * 1.0001 * math.cos(theta)

            # 颗粒大小
            granule_size = self.radius * 0.02 * granule_size_factor * (0.5 + 0.5 * np.random.random())

            # 颗粒颜色 - 随机变化
            r, g, b = self.color
            variation = 0.1 * (np.random.random() - 0.5)
            granule_r = min(1.0, max(0.0, r + variation))
            granule_g = min(1.0, max(0.0, g + variation))
            granule_b = min(1.0, max(0.0, b + variation))

            # 保存当前矩阵
            glPushMatrix()

            # 移动到颗粒位置
            glTranslatef(x, y, z)

            # 计算从原点到当前位置的方向
            length = math.sqrt(x*x + y*y + z*z)
            if length > 0:
                # 归一化方向向量
                nx, ny, nz = x/length, y/length, z/length

                # 计算旋转轴和角度
                if nz < 0.99999:  # 避免数值问题
                    # 计算旋转轴 - 叉积(0,0,1)×(nx,ny,nz)
                    axis_x = -ny
                    axis_y = nx
                    axis_z = 0

                    # 计算旋转角度 - 点积(0,0,1)·(nx,ny,nz)
                    angle = math.acos(nz) * 180.0 / math.pi

                    # 应用旋转
                    glRotatef(angle, axis_x, axis_y, axis_z)

            # 渲染颗粒 - 使用扁平圆盘
            glColor4f(granule_r, granule_g, granule_b, 0.3)
            glScalef(1.0, 1.0, 0.1)  # 使球体非常扁平，形成圆盘
            gluSphere(self.quadric, granule_size, 8, 8)

            # 恢复矩阵
            glPopMatrix()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

    def render_star_glow(self):
        """渲染恒星光晕效果"""
        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 渲染多层光晕
        num_layers = 8  # 增加光晕层数

        # 光晕颜色基于温度
        r, g, b = self.color

        # 较热的恒星有更大的光晕
        temp_factor = min(1.0, self.temperature / 10000)
        glow_size_factor = 0.5 + 0.5 * temp_factor

        for i in range(num_layers):
            # 光晕大小
            glow_size = self.radius * (1.05 + 0.15 * i * glow_size_factor)

            # 光晕透明度
            alpha = 0.2 * (1.0 - i / num_layers)

            # 渲染光晕
            glColor4f(r, g, b, alpha)
            gluSphere(self.quadric, glow_size, 32, 32)

        # 渲染光芒效果
        self.render_star_rays()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_star_rays(self):
        """渲染恒星光芒效果"""
        # 光芒数量
        num_rays = 8

        # 光芒长度 - 基于温度
        temp_factor = min(1.0, self.temperature / 10000)
        ray_length = self.radius * 3.0 * (0.5 + 0.5 * temp_factor)

        # 光芒颜色
        r, g, b = self.color

        # 渲染光芒
        glBegin(GL_LINES)
        for i in range(num_rays):
            # 光芒角度
            angle = 2.0 * math.pi * i / num_rays

            # 光芒起点
            start_x = self.radius * 1.1 * math.cos(angle)
            start_y = self.radius * 1.1 * math.sin(angle)
            start_z = 0.0

            # 光芒终点
            end_x = start_x * (1.0 + ray_length / self.radius)
            end_y = start_y * (1.0 + ray_length / self.radius)
            end_z = 0.0

            # 渲染光芒 - 从不透明到透明
            glColor4f(r, g, b, 0.5)
            glVertex3f(start_x, start_y, start_z)
            glColor4f(r, g, b, 0.0)
            glVertex3f(end_x, end_y, end_z)
        glEnd()

    def render_particles(self):
        """渲染恒星粒子"""
        # 启用点平滑
        glEnable(GL_POINT_SMOOTH)
        glHint(GL_POINT_SMOOTH_HINT, GL_NICEST)

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 渲染粒子
        for particle in self.corona_particles:
            glPointSize(particle['size'])
            glColor4f(*particle['color'], 0.7)
            glBegin(GL_POINTS)
            glVertex3f(*particle['position'])
            glEnd()

        # 渲染粒子轨迹
        glBegin(GL_LINES)
        for particle in self.corona_particles:
            if len(particle['trail']) > 1:
                for i in range(len(particle['trail']) - 1):
                    # 轨迹颜色渐变
                    alpha = i / len(particle['trail']) * 0.3
                    glColor4f(*particle['color'], alpha)
                    glVertex3f(*particle['trail'][i])
                    glVertex3f(*particle['trail'][i+1])
        glEnd()

        # 禁用混合
        glDisable(GL_BLEND)
        glDisable(GL_POINT_SMOOTH)

# 主序星类
class MainSequenceStar(Star):
    """主序星类"""

    def __init__(self, x=0, y=0, z=0, mass=1.0*M_SUN, name="主序星"):
        # 根据质量计算半径和温度 (主序星关系)
        radius = R_SUN * (mass / M_SUN)**0.8
        temperature = 5800 * (mass / M_SUN)**0.5

        super().__init__(x, y, z, mass, radius, temperature, name)
        self.type = CelestialType.STAR_MAIN_SEQUENCE

# 红巨星类
class RedGiantStar(Star):
    """红巨星类"""

    def __init__(self, x=0, y=0, z=0, mass=1.0*M_SUN, name="红巨星"):
        # 红巨星参数
        radius = R_SUN * 100 * (mass / M_SUN)
        temperature = 3500  # 较低温度

        super().__init__(x, y, z, mass, radius, temperature, name)
        self.type = CelestialType.STAR_RED_GIANT

# 蓝巨星类
class BlueGiantStar(Star):
    """蓝巨星类"""

    def __init__(self, x=0, y=0, z=0, mass=20.0*M_SUN, name="蓝巨星"):
        # 蓝巨星参数
        radius = R_SUN * 10 * (mass / (10*M_SUN))
        temperature = 20000  # 高温

        super().__init__(x, y, z, mass, radius, temperature, name)
        self.type = CelestialType.STAR_BLUE_GIANT

# 行星类
class Planet:
    """行星类"""

    def __init__(self, parent, distance, mass, radius, color, name="行星"):
        self.parent = parent  # 父天体 (恒星或其他中心天体)
        self.distance = distance  # 轨道半径
        self.mass = mass  # 质量
        self.radius = max(0.5, radius * SCALE_FACTOR)  # 可视半径
        self.color = color  # 颜色
        self.name = name

        # 轨道参数
        self.angle = np.random.random() * 2.0 * math.pi  # 初始角度
        self.orbital_speed = math.sqrt(G * parent.mass / distance) * SCALE_FACTOR * 0.5  # 轨道速度

        # 创建二次曲面对象
        self.quadric = gluNewQuadric()

    def __del__(self):
        """析构函数，释放OpenGL资源"""
        if hasattr(self, 'quadric') and self.quadric:
            gluDeleteQuadric(self.quadric)

    def update(self, dt, time_scale=1.0):
        """更新行星位置"""
        # 更新轨道角度
        self.angle += self.orbital_speed * dt * time_scale
        if self.angle > 2.0 * math.pi:
            self.angle -= 2.0 * math.pi

    def get_position(self):
        """获取行星当前位置"""
        x = self.parent.position[0] + self.distance * math.cos(self.angle)
        y = self.parent.position[1] + self.distance * math.sin(self.angle)
        z = self.parent.position[2]
        return (x, y, z)

    def render(self):
        """渲染行星"""
        # 保存当前矩阵
        glPushMatrix()

        # 移动到行星位置
        pos = self.get_position()
        glTranslatef(*pos)

        # 应用自转效应 - 行星自转
        if hasattr(self, 'rotation_angle'):
            # 自转速度与轨道速度成反比，模拟潮汐锁定效应
            self.rotation_angle += 2.0 / (1.0 + self.distance / (10.0 * self.radius))
            if self.rotation_angle > 360:
                self.rotation_angle -= 360
        else:
            self.rotation_angle = 0

        # 应用旋转 - 围绕y轴旋转（行星自转轴）
        # 添加一些倾斜，模拟行星轴倾角
        if not hasattr(self, 'axial_tilt'):
            self.axial_tilt = 23.5 * np.random.random()  # 随机轴倾角，最大23.5度

        glRotatef(self.axial_tilt, 0, 0, 1)  # 先应用轴倾角
        glRotatef(self.rotation_angle, 0, 1, 0)  # 再应用自转

        # 渲染行星表面 - 使用更复杂的渲染方法
        self.render_planet_surface()

        # 渲染行星大气层（如果有）
        if hasattr(self, 'has_atmosphere') and self.has_atmosphere:
            self.render_atmosphere()

        # 恢复矩阵
        glPopMatrix()

        # 渲染轨道
        self.render_orbit()

    def render_planet_surface(self):
        """渲染行星表面，包括纹理和细节"""
        # 基本颜色
        base_r, base_g, base_b = self.color

        # 渲染基础球体
        glColor4f(base_r, base_g, base_b, 1.0)
        gluSphere(self.quadric, self.radius, 24, 24)

        # 启用混合，用于渲染表面细节
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

        # 根据行星名称确定行星类型
        planet_type = "rocky"  # 默认为岩质行星
        if "木星" in self.name or "土星" in self.name or "天王星" in self.name or "海王星" in self.name:
            planet_type = "gas_giant"
        elif "水星" in self.name or "金星" in self.name or "地球" in self.name or "火星" in self.name:
            planet_type = "rocky"
            # 为地球添加大气层
            if "地球" in self.name:
                self.has_atmosphere = True

        if planet_type == "rocky":
            # 渲染岩质行星表面特征 - 陨石坑
            num_craters = int(10 + 30 * np.random.random())

            # 禁用深度测试，以便在球体上绘制
            glDisable(GL_DEPTH_TEST)

            for i in range(num_craters):
                # 随机位置
                theta = np.random.random() * math.pi
                phi = np.random.random() * 2.0 * math.pi

                # 计算表面位置
                x = self.radius * 1.001 * math.sin(theta) * math.cos(phi)
                y = self.radius * 1.001 * math.sin(theta) * math.sin(phi)
                z = self.radius * 1.001 * math.cos(theta)

                # 陨石坑大小
                crater_size = self.radius * (0.05 + 0.15 * np.random.random())

                # 陨石坑颜色 - 略微暗于基础颜色
                darkness = 0.7 + 0.2 * np.random.random()
                crater_r = base_r * darkness
                crater_g = base_g * darkness
                crater_b = base_b * darkness

                # 保存当前矩阵
                glPushMatrix()

                # 移动到陨石坑位置
                glTranslatef(x, y, z)

                # 渲染陨石坑 - 使用扁平球体
                glColor4f(crater_r, crater_g, crater_b, 0.7)
                glScalef(1.0, 1.0, 0.3)  # 使球体扁平，模拟陨石坑
                gluSphere(self.quadric, crater_size, 12, 12)

                # 恢复矩阵
                glPopMatrix()

            # 渲染岩质行星表面特征 - 山脉
            num_mountains = int(5 + 15 * np.random.random())

            for i in range(num_mountains):
                # 随机位置
                theta = np.random.random() * math.pi
                phi = np.random.random() * 2.0 * math.pi

                # 计算表面位置
                x = self.radius * math.sin(theta) * math.cos(phi)
                y = self.radius * math.sin(theta) * math.sin(phi)
                z = self.radius * math.cos(theta)

                # 山脉大小
                mountain_size = self.radius * (0.1 + 0.2 * np.random.random())

                # 山脉颜色 - 略微亮于基础颜色
                brightness = 1.1 + 0.1 * np.random.random()
                mountain_r = min(1.0, base_r * brightness)
                mountain_g = min(1.0, base_g * brightness)
                mountain_b = min(1.0, base_b * brightness)

                # 保存当前矩阵
                glPushMatrix()

                # 移动到山脉位置并旋转，使山脉指向行星中心
                glTranslatef(x, y, z)

                # 计算从原点到当前位置的方向
                length = math.sqrt(x*x + y*y + z*z)
                if length > 0:
                    # 归一化方向向量
                    nx, ny, nz = x/length, y/length, z/length

                    # 计算旋转轴和角度
                    # 默认方向是(0,0,1)，我们需要旋转到(nx,ny,nz)
                    if nz < 0.99999:  # 避免数值问题
                        # 计算旋转轴 - 叉积(0,0,1)×(nx,ny,nz)
                        axis_x = -ny
                        axis_y = nx
                        axis_z = 0

                        # 计算旋转角度 - 点积(0,0,1)·(nx,ny,nz)
                        angle = math.acos(nz) * 180.0 / math.pi

                        # 应用旋转
                        glRotatef(angle, axis_x, axis_y, axis_z)

                # 渲染山脉 - 使用锥体
                glColor4f(mountain_r, mountain_g, mountain_b, 0.7)

                # 绘制锥体
                glBegin(GL_TRIANGLE_FAN)
                glVertex3f(0, 0, mountain_size)  # 顶点

                # 底部圆周顶点
                segments = 16
                for j in range(segments + 1):
                    angle = j * 2.0 * math.pi / segments
                    glVertex3f(mountain_size * 0.5 * math.cos(angle),
                              mountain_size * 0.5 * math.sin(angle),
                              0)
                glEnd()

                # 恢复矩阵
                glPopMatrix()

        elif planet_type == "gas_giant":
            # 渲染气态巨行星表面特征 - 条带
            num_bands = int(5 + 5 * np.random.random())

            for i in range(num_bands):
                # 计算带的位置 - 均匀分布在行星表面
                theta = math.pi * (i + 0.5) / num_bands

                # 带的宽度
                band_width = math.pi / (num_bands * (0.8 + 0.4 * np.random.random()))

                # 带的颜色 - 随机变化
                variation = 0.2 * (np.random.random() - 0.5)
                band_r = min(1.0, max(0.0, base_r + variation))
                band_g = min(1.0, max(0.0, base_g + variation))
                band_b = min(1.0, max(0.0, base_b + variation))

                # 渲染带 - 使用一系列四边形
                glColor4f(band_r, band_g, band_b, 0.5)

                segments = 32
                glBegin(GL_QUAD_STRIP)
                for j in range(segments + 1):
                    phi = j * 2.0 * math.pi / segments

                    # 带的上边缘
                    x1 = self.radius * 1.001 * math.sin(theta - band_width/2) * math.cos(phi)
                    y1 = self.radius * 1.001 * math.sin(theta - band_width/2) * math.sin(phi)
                    z1 = self.radius * 1.001 * math.cos(theta - band_width/2)

                    # 带的下边缘
                    x2 = self.radius * 1.001 * math.sin(theta + band_width/2) * math.cos(phi)
                    y2 = self.radius * 1.001 * math.sin(theta + band_width/2) * math.sin(phi)
                    z2 = self.radius * 1.001 * math.cos(theta + band_width/2)

                    glVertex3f(x1, y1, z1)
                    glVertex3f(x2, y2, z2)
                glEnd()

            # 渲染气态巨行星表面特征 - 大气风暴
            num_storms = int(2 + 3 * np.random.random())

            for i in range(num_storms):
                # 随机位置
                theta = np.random.random() * math.pi
                phi = np.random.random() * 2.0 * math.pi

                # 计算表面位置
                x = self.radius * 1.001 * math.sin(theta) * math.cos(phi)
                y = self.radius * 1.001 * math.sin(theta) * math.sin(phi)
                z = self.radius * 1.001 * math.cos(theta)

                # 风暴大小
                storm_size = self.radius * (0.1 + 0.3 * np.random.random())

                # 风暴颜色 - 随机变化
                variation = 0.3 * (np.random.random() - 0.5)
                storm_r = min(1.0, max(0.0, base_r + variation))
                storm_g = min(1.0, max(0.0, base_g + variation))
                storm_b = min(1.0, max(0.0, base_b + variation))

                # 保存当前矩阵
                glPushMatrix()

                # 移动到风暴位置
                glTranslatef(x, y, z)

                # 计算从原点到当前位置的方向
                length = math.sqrt(x*x + y*y + z*z)
                if length > 0:
                    # 归一化方向向量
                    nx, ny, nz = x/length, y/length, z/length

                    # 计算旋转轴和角度
                    if nz < 0.99999:  # 避免数值问题
                        # 计算旋转轴 - 叉积(0,0,1)×(nx,ny,nz)
                        axis_x = -ny
                        axis_y = nx
                        axis_z = 0

                        # 计算旋转角度 - 点积(0,0,1)·(nx,ny,nz)
                        angle = math.acos(nz) * 180.0 / math.pi

                        # 应用旋转
                        glRotatef(angle, axis_x, axis_y, axis_z)

                # 渲染风暴 - 使用椭圆形
                glColor4f(storm_r, storm_g, storm_b, 0.6)

                # 使风暴椭圆形
                glScalef(1.0, 0.6, 0.1)
                gluSphere(self.quadric, storm_size, 16, 16)

                # 恢复矩阵
                glPopMatrix()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_atmosphere(self):
        """渲染行星大气层"""
        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 大气层颜色 - 蓝色调
        atm_r = 0.5
        atm_g = 0.7
        atm_b = 1.0

        # 渲染大气层 - 半透明球体
        glColor4f(atm_r, atm_g, atm_b, 0.2)
        gluSphere(self.quadric, self.radius * 1.05, 32, 32)

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

    def render_orbit(self):
        """渲染行星轨道"""
        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 渲染轨道
        glColor4f(0.5, 0.5, 0.5, 0.3)
        glBegin(GL_LINE_LOOP)
        for i in range(60):
            angle = i * 2.0 * math.pi / 60
            x = self.parent.position[0] + self.distance * math.cos(angle)
            y = self.parent.position[1] + self.distance * math.sin(angle)
            z = self.parent.position[2]
            glVertex3f(x, y, z)
        glEnd()

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

        # 禁用混合
        glDisable(GL_BLEND)

# 行星系统类
class PlanetSystem(CelestialBody):
    """行星系统类"""

    def __init__(self, x=0, y=0, z=0, name="行星系统"):
        # 创建中心恒星
        self.star = MainSequenceStar(x, y, z, 1.0*M_SUN, "中心恒星")

        # 初始化基类
        super().__init__(x, y, z, self.star.mass, name)
        self.type = CelestialType.PLANET_SYSTEM
        self.radius = self.star.radius  # 使用恒星半径
        self.color = self.star.color  # 使用恒星颜色

        # 创建行星
        self.planets = []
        self.create_planets()

        # 时间缩放因子
        self.time_scale = 1.0

        # 初始化粒子系统属性，避免属性错误
        self.disk_particles = []
        self.jet_particles = []
        self.spin_parameter = 0.0  # 行星系统没有自旋

    def create_planets(self):
        """创建行星系统中的行星"""
        # 类地行星
        earth = Planet(
            self.star,
            distance=1.0*AU*SCALE_FACTOR,
            mass=5.97e24,
            radius=6.37e6,
            color=(0.2, 0.4, 0.8),
            name="地球"
        )
        self.planets.append(earth)

        # 类木行星
        jupiter = Planet(
            self.star,
            distance=5.2*AU*SCALE_FACTOR,
            mass=1.9e27,
            radius=7.14e7,
            color=(0.8, 0.7, 0.5),
            name="木星"
        )
        self.planets.append(jupiter)

        # 类水星
        mercury = Planet(
            self.star,
            distance=0.4*AU*SCALE_FACTOR,
            mass=3.3e23,
            radius=2.44e6,
            color=(0.7, 0.7, 0.7),
            name="水星"
        )
        self.planets.append(mercury)

        # 类金星
        venus = Planet(
            self.star,
            distance=0.7*AU*SCALE_FACTOR,
            mass=4.87e24,
            radius=6.05e6,
            color=(0.9, 0.7, 0.4),
            name="金星"
        )
        self.planets.append(venus)

        # 类火星
        mars = Planet(
            self.star,
            distance=1.5*AU*SCALE_FACTOR,
            mass=6.42e23,
            radius=3.4e6,
            color=(0.8, 0.4, 0.2),
            name="火星"
        )
        self.planets.append(mars)

    def update(self, dt):
        """更新行星系统"""
        # 更新恒星
        self.star.update(dt)

        # 更新行星
        for planet in self.planets:
            planet.update(dt, self.time_scale)

    def render(self):
        """渲染行星系统"""
        # 渲染恒星
        self.star.render()

        # 渲染行星
        for planet in self.planets:
            planet.render()

    def set_time_scale(self, scale):
        """设置时间缩放因子"""
        self.time_scale = max(0.0, min(100.0, scale))
        return self.time_scale

    def calculate_photon_sphere(self, _):
        """计算光子球半径"""
        # 简化的光子球计算，参数是自旋参数
        # 在简化版本中，我们不使用自旋参数
        return 3.0 * self.schwarzschild_radius / 2.0 * SCALE_FACTOR

    def calculate_isco(self, _):
        """计算最后稳定圆轨道半径"""
        # 简化的ISCO计算，参数是自旋参数
        # 在简化版本中，我们不使用自旋参数
        return 3.0 * self.schwarzschild_radius * SCALE_FACTOR

    def generate_accretion_disk(self, num_particles):
        """生成吸积盘粒子"""
        for i in range(num_particles):
            # 随机角度和距离
            angle = 2.0 * math.pi * i / num_particles
            distance = self.isco_radius + (10.0 * self.radius - self.isco_radius) * np.random.random()

            # 计算位置
            x = self.position[0] + distance * math.cos(angle)
            y = self.position[1] + distance * math.sin(angle)
            z = self.position[2] + (np.random.random() - 0.5) * 0.5

            # 计算轨道速度
            orbital_speed = math.sqrt(G * self.mass / distance) * SCALE_FACTOR * 5.0
            vx = -orbital_speed * math.sin(angle)
            vy = orbital_speed * math.cos(angle)
            vz = 0.0

            # 粒子颜色 - 更鲜艳的颜色
            r = 0.8 + 0.2 * np.random.random()
            g = 0.6 * np.random.random()
            b = 0.2 + 0.3 * np.random.random()

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': 2.0 + np.random.random(),
                'distance': distance,
                'angle': angle,
                'trail': []
            }

            self.disk_particles.append(particle)

    def generate_jets(self, num_particles):
        """生成喷流粒子"""
        for i in range(num_particles):
            # 随机方向 (上/下)
            direction = 1 if i < num_particles / 2 else -1

            # 随机距离
            distance = 2.0 + 3.0 * np.random.random()

            # 随机角度和半径
            angle = 2.0 * math.pi * np.random.random()
            radius = 0.5 * np.random.random()

            # 计算位置
            x = self.position[0] + radius * math.cos(angle)
            y = self.position[1] + radius * math.sin(angle)
            z = self.position[2] + direction * distance

            # 计算速度
            speed = (0.1 + 0.2 * np.random.random()) * C * SCALE_FACTOR * 0.01
            vx = 0.0
            vy = 0.0
            vz = direction * speed

            # 粒子颜色
            if direction > 0:  # 蓝色喷流
                r = 0.2 * np.random.random()
                g = 0.4 + 0.4 * np.random.random()
                b = 0.7 + 0.3 * np.random.random()
            else:  # 红色喷流
                r = 0.7 + 0.3 * np.random.random()
                g = 0.4 + 0.4 * np.random.random()
                b = 0.2 * np.random.random()

            # 创建粒子
            particle = {
                'id': i,
                'position': [x, y, z],
                'velocity': [vx, vy, vz],
                'color': (r, g, b),
                'size': 1.5 + np.random.random(),
                'direction': direction,
                'trail': []
            }

            self.jet_particles.append(particle)

    def update_accretion_disk_particles(self, dt):
        """更新吸积盘粒子"""
        # 使用CUDA加速粒子更新
        if CUDA_AVAILABLE:
            self._update_accretion_disk_particles_cuda(dt)
        else:
            self._update_accretion_disk_particles_cpu(dt)

    def _update_accretion_disk_particles_cuda(self, dt):
        """使用CUDA加速更新吸积盘粒子"""
        try:
            from numba import cuda
            import numpy as np

            # 更新所有粒子
            for particle in self.disk_particles:
                # 更新位置
                particle['position'][0] += particle['velocity'][0] * dt
                particle['position'][1] += particle['velocity'][1] * dt
                particle['position'][2] += particle['velocity'][2] * dt

                # 更新轨迹
                if len(particle['trail']) > 20:
                    particle['trail'].pop(0)
                particle['trail'].append(particle['position'].copy())

                # 计算到黑洞的距离 - 使用向量化操作
                pos = np.array(particle['position'])
                bh_pos = np.array(self.position)
                delta = pos - bh_pos
                distance = np.sqrt(np.sum(delta**2))

                # 更新距离和角度
                particle['distance'] = distance
                particle['angle'] = math.atan2(delta[1], delta[0])

                # 如果粒子太靠近黑洞，重置到外部区域
                if distance < self.isco_radius:
                    # 生成新角度
                    angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                    new_distance = self.radius * 10.0 * (0.8 + 0.2 * np.random.random())

                    particle['position'][0] = self.position[0] + new_distance * math.cos(angle)
                    particle['position'][1] = self.position[1] + new_distance * math.sin(angle)
                    particle['position'][2] = self.position[2] + (particle['id'] % 10) * 0.1 - 0.5

                    # 计算新的轨道速度
                    orbital_speed = math.sqrt(G * self.mass / new_distance) * SCALE_FACTOR * 5.0
                    particle['velocity'][0] = -orbital_speed * math.sin(angle)
                    particle['velocity'][1] = orbital_speed * math.cos(angle)
                    particle['velocity'][2] = 0.0

                    # 清除轨迹
                    particle['trail'] = []

                    # 更新粒子颜色 - 更鲜艳的颜色
                    r = 0.8 + 0.2 * np.random.random()
                    g = 0.6 * np.random.random()
                    b = 0.2 + 0.3 * np.random.random()
                    particle['color'] = (r, g, b)

                    # 更新粒子大小
                    particle['size'] = 2.0 + np.random.random()
        except Exception as e:
            print(f"CUDA加速失败，降级为CPU计算: {e}")
            self._update_accretion_disk_particles_cpu(dt)

    def _update_accretion_disk_particles_cpu(self, dt):
        """使用CPU更新吸积盘粒子"""
        for particle in self.disk_particles:
            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹
            if len(particle['trail']) > 20:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 计算到黑洞的距离
            dx = particle['position'][0] - self.position[0]
            dy = particle['position'][1] - self.position[1]
            dz = particle['position'][2] - self.position[2]
            distance = math.sqrt(dx*dx + dy*dy + dz*dz)

            # 更新距离和角度
            particle['distance'] = distance
            particle['angle'] = math.atan2(dy, dx)

            # 如果粒子太靠近黑洞，重置到外部区域
            if distance < self.isco_radius:
                # 生成新角度
                angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                new_distance = self.radius * 10.0 * (0.8 + 0.2 * np.random.random())

                particle['position'][0] = self.position[0] + new_distance * math.cos(angle)
                particle['position'][1] = self.position[1] + new_distance * math.sin(angle)
                particle['position'][2] = self.position[2] + (particle['id'] % 10) * 0.1 - 0.5

                # 计算新的轨道速度
                orbital_speed = math.sqrt(G * self.mass / new_distance) * SCALE_FACTOR * 5.0
                particle['velocity'][0] = -orbital_speed * math.sin(angle)
                particle['velocity'][1] = orbital_speed * math.cos(angle)
                particle['velocity'][2] = 0.0

                # 清除轨迹
                particle['trail'] = []

                # 更新粒子颜色
                r = 0.8 + 0.2 * np.random.random()
                g = 0.6 * np.random.random()
                b = 0.2 + 0.3 * np.random.random()
                particle['color'] = (r, g, b)

                # 更新粒子大小
                particle['size'] = 2.0 + np.random.random()

    def update_jet_particles(self, dt):
        """更新喷流粒子"""
        # 使用CUDA加速粒子更新
        if CUDA_AVAILABLE:
            self._update_jet_particles_cuda(dt)
        else:
            self._update_jet_particles_cpu(dt)

    def _update_jet_particles_cuda(self, dt):
        """使用CUDA加速更新喷流粒子"""
        try:
            from numba import cuda
            import numpy as np

            # 更新所有粒子
            for particle in self.jet_particles:
                # 更新位置
                particle['position'][0] += particle['velocity'][0] * dt
                particle['position'][1] += particle['velocity'][1] * dt
                particle['position'][2] += particle['velocity'][2] * dt

                # 更新轨迹
                if len(particle['trail']) > 20:
                    particle['trail'].pop(0)
                particle['trail'].append(particle['position'].copy())

                # 如果粒子太远，重置到喷流基部
                dz = abs(particle['position'][2] - self.position[2])
                if dz > self.radius * 15.0:
                    direction = particle['direction']
                    distance = 2.0  # 初始距离

                    # 在轴附近生成新位置
                    angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                    radius = 0.5 * (particle['id'] % 10) / 10.0

                    particle['position'][0] = self.position[0] + radius * math.cos(angle)
                    particle['position'][1] = self.position[1] + radius * math.sin(angle)
                    particle['position'][2] = self.position[2] + direction * distance

                    # 新速度 - 更快的喷流速度
                    speed = (0.15 + (particle['id'] % 5) * 0.12) * C * SCALE_FACTOR * 0.01
                    particle['velocity'][0] = 0.0
                    particle['velocity'][1] = 0.0
                    particle['velocity'][2] = direction * speed

                    # 清除轨迹
                    particle['trail'] = []
        except Exception as e:
            print(f"CUDA加速失败，降级为CPU计算: {e}")
            self._update_jet_particles_cpu(dt)

    def _update_jet_particles_cpu(self, dt):
        """使用CPU更新喷流粒子"""
        for particle in self.jet_particles:
            # 更新位置
            particle['position'][0] += particle['velocity'][0] * dt
            particle['position'][1] += particle['velocity'][1] * dt
            particle['position'][2] += particle['velocity'][2] * dt

            # 更新轨迹
            if len(particle['trail']) > 20:
                particle['trail'].pop(0)
            particle['trail'].append(particle['position'].copy())

            # 如果粒子太远，重置到喷流基部
            dz = abs(particle['position'][2] - self.position[2])
            if dz > self.radius * 15.0:
                direction = particle['direction']
                distance = 2.0  # 初始距离

                # 在轴附近生成新位置
                angle = (particle['id'] * 0.1 + dt * 10.0) % (2.0 * math.pi)
                radius = 0.5 * (particle['id'] % 10) / 10.0

                particle['position'][0] = self.position[0] + radius * math.cos(angle)
                particle['position'][1] = self.position[1] + radius * math.sin(angle)
                particle['position'][2] = self.position[2] + direction * distance

                # 新速度
                speed = (0.1 + (particle['id'] % 5) * 0.1) * C * SCALE_FACTOR * 0.01
                particle['velocity'][0] = 0.0
                particle['velocity'][1] = 0.0
                particle['velocity'][2] = direction * speed

                # 清除轨迹
                particle['trail'] = []

    def update_black_hole_physics(self, dt):
        """更新黑洞物理"""
        # 记录开始时间以计算物理更新时间
        start_time = time.time()

        # 更新吸积盘粒子
        self.update_accretion_disk_particles(dt)

        # 更新喷流粒子
        if self.spin_parameter > 0.6:
            self.update_jet_particles(dt)

        # 计算物理更新时间
        physics_time = (time.time() - start_time) * 1000

        # 每100帧打印一次物理更新时间
        if int(time.time() * 10) % 100 == 0:
            mode = "RTX GPU" if CUDA_AVAILABLE and RTX_ENABLED else "CPU"
            print(f"物理更新时间: {physics_time:.2f}ms | 模式: {mode}")

    def render_black_hole(self):
        """渲染黑洞"""
        # 保存当前矩阵
        glPushMatrix()

        # 移动到黑洞位置
        glTranslatef(*self.position)

        # 渲染事件视界 (黑色球体)
        glColor4f(0.0, 0.0, 0.0, 1.0)
        gluSphere(self.quadric, self.radius, 32, 32)

        # 渲染光子球 (半透明蓝色球体)
        glColor4f(0.0, 0.3, 0.8, 0.2)
        gluSphere(self.quadric, self.photon_sphere_radius, 32, 32)

        # 渲染吸积盘粒子
        self.render_particles(self.disk_particles)

        # 渲染喷流粒子
        if self.spin_parameter > 0.6:
            self.render_particles(self.jet_particles)

        # 恢复矩阵
        glPopMatrix()

    def render_particles(self, particles):
        """渲染粒子"""
        # 启用点平滑
        glEnable(GL_POINT_SMOOTH)
        glHint(GL_POINT_SMOOTH_HINT, GL_NICEST)

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 先渲染粒子轨迹，使其在粒子后面
        glLineWidth(2.0)  # 增加轨迹线宽
        glBegin(GL_LINES)
        for particle in particles:
            if len(particle['trail']) > 1:
                # 获取粒子颜色
                r, g, b = particle['color']

                # 获取粒子旋转速度（如果有）
                rotation_speed = particle.get('rotation_speed', 1.0)

                # 根据旋转速度调整轨迹亮度
                brightness_factor = min(1.0, rotation_speed * 0.5) if hasattr(self, 'spin_parameter') else 0.5

                for i in range(len(particle['trail']) - 1):
                    # 轨迹颜色渐变 - 使轨迹更加明显
                    alpha = (i / len(particle['trail'])) * brightness_factor

                    # 调整轨迹颜色 - 使其更加鲜艳
                    trail_r = min(1.0, r * 1.2)
                    trail_g = min(1.0, g * 1.2)
                    trail_b = min(1.0, b * 1.2)

                    glColor4f(trail_r, trail_g, trail_b, alpha * 0.7)  # 增加轨迹透明度
                    glVertex3f(*particle['trail'][i])
                    glVertex3f(*particle['trail'][i+1])
        glEnd()

        # 渲染粒子
        for particle in particles:
            # 获取粒子旋转速度（如果有）
            rotation_speed = particle.get('rotation_speed', 1.0)

            # 根据旋转速度调整粒子大小
            size_factor = 1.0
            if hasattr(self, 'spin_parameter') and 'rotation_speed' in particle:
                size_factor = 1.0 + 0.5 * min(1.0, rotation_speed * 0.5)

            # 设置粒子大小
            glPointSize(particle['size'] * size_factor)

            # 设置粒子颜色
            r, g, b = particle['color']

            # 调整粒子颜色 - 使其更加明亮
            particle_r = min(1.0, r * 1.2)
            particle_g = min(1.0, g * 1.2)
            particle_b = min(1.0, b * 1.2)

            glColor4f(particle_r, particle_g, particle_b, 1.0)
            glBegin(GL_POINTS)
            glVertex3f(*particle['position'])
            glEnd()

            # 为高速旋转的粒子添加光晕效果
            if hasattr(self, 'spin_parameter') and 'rotation_speed' in particle and rotation_speed > 1.0:
                # 光晕大小与旋转速度成正比
                halo_size = particle['size'] * (1.0 + 0.5 * min(2.0, rotation_speed))

                # 光晕透明度
                halo_alpha = 0.3 * min(1.0, rotation_speed * 0.3)

                # 渲染光晕
                glPointSize(halo_size)
                glColor4f(particle_r, particle_g, particle_b, halo_alpha)
                glBegin(GL_POINTS)
                glVertex3f(*particle['position'])
                glEnd()

        # 禁用混合
        glDisable(GL_BLEND)
        glDisable(GL_POINT_SMOOTH)

# 模拟类
class Simulation:
    def __init__(self, width=1280, height=720):
        pygame.init()
        self.width = width
        self.height = height

        # 相机控制
        self.camera_distance = 40.0
        self.min_camera_distance = 5.0
        self.max_camera_distance = 200.0
        self.zoom_speed = 1.0
        self.fov = 45.0  # 视场角 (度)
        self.min_fov = 10.0
        self.max_fov = 120.0

        # 优化的按键控制参数
        self.key_repeat_delay = 0.1  # 减少按键重复延迟（秒）
        self.key_repeat_interval = 0.02  # 减少按键重复间隔（秒）
        self.key_timers = {}  # 按键计时器
        self.key_states = {}  # 按键状态跟踪

        # 初始化OpenGL窗口
        self.screen = pygame.display.set_mode((width, height), DOUBLEBUF | OPENGL)
        pygame.display.set_caption("天体物理模拟程序")

        # 设置OpenGL
        glEnable(GL_DEPTH_TEST)
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)

        # 设置背景色 (深空黑色)
        glClearColor(0.0, 0.0, 0.02, 1.0)

        # 设置投影
        self.update_projection()

        # 设置视图
        glMatrixMode(GL_MODELVIEW)
        glLoadIdentity()
        glTranslatef(0.0, 0.0, -self.camera_distance)
        glRotatef(30.0, 1.0, 0.0, 0.0)  # 添加初始倾斜角度

        # 创建天体
        self.celestial_bodies = []
        self.current_body_index = 0
        self.create_celestial_bodies()
        self.current_body = self.celestial_bodies[self.current_body_index]

        # 旋转控制
        self.rotation_x = 30.0
        self.rotation_y = 0.0

        # 时间控制
        self.clock = pygame.time.Clock()
        self.time = 0.0
        self.time_scale = 1.0  # 时间流速缩放因子
        self.paused = False
        self.running = True

        # 性能监控
        self.fps_list = []
        self.last_time = time.time()
        self.frame_count = 0

        # 用户界面状态
        self.show_help = False
        self.show_info = True

        print("模拟初始化完成")

    def create_celestial_bodies(self):
        """创建各种天体"""
        # 克尔黑洞
        kerr_black_hole = BlackHole(0, 0, 0, 1e6)
        kerr_black_hole.name = "克尔黑洞 (有自旋)"
        self.celestial_bodies.append(kerr_black_hole)

        # 史瓦西黑洞
        schwarzschild_black_hole = SchwarzschildBlackHole(0, 0, 0, 1e6)
        self.celestial_bodies.append(schwarzschild_black_hole)

        # 中子星
        neutron_star = NeutronStar(0, 0, 0, 2.0*M_SUN)
        self.celestial_bodies.append(neutron_star)

        # 主序星
        main_sequence_star = MainSequenceStar(0, 0, 0, 1.0*M_SUN)
        self.celestial_bodies.append(main_sequence_star)

        # 红巨星
        red_giant = RedGiantStar(0, 0, 0, 1.5*M_SUN)
        self.celestial_bodies.append(red_giant)

        # 蓝巨星
        blue_giant = BlueGiantStar(0, 0, 0, 20.0*M_SUN)
        self.celestial_bodies.append(blue_giant)

        # 行星系统
        planet_system = PlanetSystem(0, 0, 0)
        self.celestial_bodies.append(planet_system)

    def update_projection(self):
        """更新投影矩阵"""
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        gluPerspective(self.fov, (self.width/self.height), 0.1, 1000.0)

    def handle_events(self):
        """处理事件"""
        # 处理单次事件
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                self.handle_key_down(event)
            elif event.type == pygame.KEYUP:
                self.handle_key_up(event)
            elif event.type == pygame.MOUSEBUTTONDOWN:
                self.handle_mouse_button_down(event)
            elif event.type == pygame.MOUSEMOTION:
                self.handle_mouse_motion(event)
            elif event.type == pygame.MOUSEWHEEL:
                self.handle_mouse_wheel(event)

        # 处理持续按下的按键
        self.handle_key_pressed()

    def handle_key_down(self, event):
        """处理键盘按下事件 - 优化响应性"""
        # 记录按键按下的时间和状态
        current_time = time.time()
        self.key_timers[event.key] = current_time
        self.key_states[event.key] = True

        # 处理单次按键事件 - 立即响应
        if event.key == pygame.K_ESCAPE:
            self.running = False
            print("程序退出")
        elif event.key == pygame.K_UP:
            # 减小相机距离 (放大) - 增强响应
            self.camera_distance = max(self.min_camera_distance,
                                      self.camera_distance - 3.0 * self.zoom_speed)
        elif event.key == pygame.K_DOWN:
            # 增加相机距离 (缩小) - 增强响应
            self.camera_distance = min(self.max_camera_distance,
                                      self.camera_distance + 3.0 * self.zoom_speed)
        elif event.key == pygame.K_LEFT:
            # 左旋转 - 增强响应
            self.rotation_y -= 3.0
        elif event.key == pygame.K_RIGHT:
            # 右旋转 - 增强响应
            self.rotation_y += 3.0
        elif event.key == pygame.K_PAGEUP:
            # 减小FOV (放大) - 增强响应
            self.fov = max(self.min_fov, self.fov - 3.0)
            self.update_projection()
        elif event.key == pygame.K_PAGEDOWN:
            # 增加FOV (缩小) - 增强响应
            self.fov = min(self.max_fov, self.fov + 3.0)
            self.update_projection()
        elif event.key == pygame.K_SPACE:
            # 暂停/继续 - 立即响应
            self.paused = not self.paused
            status = "暂停" if self.paused else "继续"
            print(f"模拟{status}")
        elif event.key == pygame.K_EQUALS or event.key == pygame.K_PLUS:
            # 增加时间流速 - 立即响应
            old_scale = self.time_scale
            self.time_scale = min(10.0, self.time_scale * 1.5)
            if isinstance(self.current_body, PlanetSystem):
                self.current_body.set_time_scale(self.time_scale)
            print(f"时间流速: {old_scale:.1f}x → {self.time_scale:.1f}x")
        elif event.key == pygame.K_MINUS:
            # 减小时间流速 - 立即响应
            old_scale = self.time_scale
            self.time_scale = max(0.1, self.time_scale / 1.5)
            if isinstance(self.current_body, PlanetSystem):
                self.current_body.set_time_scale(self.time_scale)
            print(f"时间流速: {old_scale:.1f}x → {self.time_scale:.1f}x")
        elif event.key == pygame.K_r:
            # 重置时间流速 - 立即响应
            self.time_scale = 1.0
            if isinstance(self.current_body, PlanetSystem):
                self.current_body.set_time_scale(self.time_scale)
            print("时间流速重置为 1.0x")
        elif event.key == pygame.K_TAB:
            # 切换天体 - 立即响应
            self.current_body_index = (self.current_body_index + 1) % len(self.celestial_bodies)
            self.current_body = self.celestial_bodies[self.current_body_index]
            # 重置相机距离
            if isinstance(self.current_body, PlanetSystem):
                self.camera_distance = 60.0
            elif isinstance(self.current_body, Star):
                self.camera_distance = 30.0
            else:
                self.camera_distance = 40.0
            print(f"切换天体: {self.current_body.name}")
        elif event.key == pygame.K_h:
            # 显示/隐藏帮助 - 立即响应
            self.show_help = not self.show_help
            status = "显示" if self.show_help else "隐藏"
            print(f"{status}帮助信息")
        elif event.key == pygame.K_i:
            # 显示/隐藏信息 - 立即响应
            self.show_info = not self.show_info
            status = "显示" if self.show_info else "隐藏"
            print(f"{status}状态信息")
        elif event.key == pygame.K_1:
            # 切换到克尔黑洞 - 立即响应
            self.current_body_index = 0
            self.current_body = self.celestial_bodies[self.current_body_index]
            self.camera_distance = 40.0
            print(f"切换到: {self.current_body.name}")
        elif event.key == pygame.K_2:
            # 切换到史瓦西黑洞 - 立即响应
            if len(self.celestial_bodies) > 1:
                self.current_body_index = 1
                self.current_body = self.celestial_bodies[self.current_body_index]
                self.camera_distance = 40.0
                print(f"切换到: {self.current_body.name}")
        elif event.key == pygame.K_3:
            # 切换到中子星 - 立即响应
            if len(self.celestial_bodies) > 2:
                self.current_body_index = 2
                self.current_body = self.celestial_bodies[self.current_body_index]
                self.camera_distance = 30.0
                print(f"切换到: {self.current_body.name}")
        elif event.key == pygame.K_4:
            # 切换到主序星 - 立即响应
            if len(self.celestial_bodies) > 3:
                self.current_body_index = 3
                self.current_body = self.celestial_bodies[self.current_body_index]
                self.camera_distance = 30.0
                print(f"切换到: {self.current_body.name}")
        elif event.key == pygame.K_5:
            # 切换到红巨星 - 立即响应
            if len(self.celestial_bodies) > 4:
                self.current_body_index = 4
                self.current_body = self.celestial_bodies[self.current_body_index]
                self.camera_distance = 40.0
                print(f"切换到: {self.current_body.name}")
        elif event.key == pygame.K_6:
            # 切换到蓝巨星 - 立即响应
            if len(self.celestial_bodies) > 5:
                self.current_body_index = 5
                self.current_body = self.celestial_bodies[self.current_body_index]
                self.camera_distance = 40.0
                print(f"切换到: {self.current_body.name}")
        elif event.key == pygame.K_7:
            # 切换到行星系统 - 立即响应
            if len(self.celestial_bodies) > 6:
                self.current_body_index = 6
                self.current_body = self.celestial_bodies[self.current_body_index]
                self.camera_distance = 60.0
                print(f"切换到: {self.current_body.name}")

    def handle_key_up(self, event):
        """处理键盘释放事件"""
        # 清除按键状态
        if event.key in self.key_states:
            self.key_states[event.key] = False
        if event.key in self.key_timers:
            del self.key_timers[event.key]

    def handle_mouse_button_down(self, event):
        """处理鼠标按下事件"""
        if event.button == 4:  # 滚轮向上
            # 减小相机距离 (放大)
            self.camera_distance = max(self.min_camera_distance,
                                      self.camera_distance - 2.0 * self.zoom_speed)
        elif event.button == 5:  # 滚轮向下
            # 增加相机距离 (缩小)
            self.camera_distance = min(self.max_camera_distance,
                                      self.camera_distance + 2.0 * self.zoom_speed)

    def handle_mouse_motion(self, event):
        """处理鼠标移动事件"""
        if pygame.mouse.get_pressed()[0]:  # 左键拖动
            dx, dy = event.rel
            self.rotation_y += dx * 0.5
            self.rotation_x += dy * 0.5

            # 限制x轴旋转角度
            self.rotation_x = max(-90, min(90, self.rotation_x))

    def handle_mouse_wheel(self, event):
        """处理鼠标滚轮事件"""
        # 减小相机距离 (放大)
        if event.y > 0:
            self.camera_distance = max(self.min_camera_distance,
                                      self.camera_distance - 2.0 * self.zoom_speed)
        # 增加相机距离 (缩小)
        elif event.y < 0:
            self.camera_distance = min(self.max_camera_distance,
                                      self.camera_distance + 2.0 * self.zoom_speed)

    def handle_key_pressed(self):
        """处理持续按下的按键 - 优化流畅性"""
        # 获取当前按下的所有按键
        keys = pygame.key.get_pressed()
        current_time = time.time()

        # 处理方向键持续按下 - 更流畅的响应
        if keys[pygame.K_UP] and pygame.K_UP in self.key_states and self.key_states[pygame.K_UP]:
            # 检查是否已经过了重复延迟时间
            if pygame.K_UP in self.key_timers and current_time - self.key_timers[pygame.K_UP] > self.key_repeat_delay:
                # 减小相机距离 (放大) - 增强流畅性
                self.camera_distance = max(self.min_camera_distance,
                                          self.camera_distance - 0.8 * self.zoom_speed)
                # 更新计时器，使用重复间隔
                self.key_timers[pygame.K_UP] = current_time - self.key_repeat_delay + self.key_repeat_interval

        if keys[pygame.K_DOWN] and pygame.K_DOWN in self.key_states and self.key_states[pygame.K_DOWN]:
            if pygame.K_DOWN in self.key_timers and current_time - self.key_timers[pygame.K_DOWN] > self.key_repeat_delay:
                # 增加相机距离 (缩小) - 增强流畅性
                self.camera_distance = min(self.max_camera_distance,
                                          self.camera_distance + 0.8 * self.zoom_speed)
                self.key_timers[pygame.K_DOWN] = current_time - self.key_repeat_delay + self.key_repeat_interval

        if keys[pygame.K_LEFT] and pygame.K_LEFT in self.key_states and self.key_states[pygame.K_LEFT]:
            if pygame.K_LEFT in self.key_timers and current_time - self.key_timers[pygame.K_LEFT] > self.key_repeat_delay:
                # 左旋转 - 增强流畅性
                self.rotation_y -= 1.5
                self.key_timers[pygame.K_LEFT] = current_time - self.key_repeat_delay + self.key_repeat_interval

        if keys[pygame.K_RIGHT] and pygame.K_RIGHT in self.key_states and self.key_states[pygame.K_RIGHT]:
            if pygame.K_RIGHT in self.key_timers and current_time - self.key_timers[pygame.K_RIGHT] > self.key_repeat_delay:
                # 右旋转 - 增强流畅性
                self.rotation_y += 1.5
                self.key_timers[pygame.K_RIGHT] = current_time - self.key_repeat_delay + self.key_repeat_interval

        # 处理其他持续按键 - 增强流畅性
        if keys[pygame.K_PAGEUP] and pygame.K_PAGEUP in self.key_states and self.key_states[pygame.K_PAGEUP]:
            if pygame.K_PAGEUP in self.key_timers and current_time - self.key_timers[pygame.K_PAGEUP] > self.key_repeat_delay:
                # 减小FOV (放大) - 增强流畅性
                self.fov = max(self.min_fov, self.fov - 0.8)
                self.update_projection()
                self.key_timers[pygame.K_PAGEUP] = current_time - self.key_repeat_delay + self.key_repeat_interval

        if keys[pygame.K_PAGEDOWN] and pygame.K_PAGEDOWN in self.key_states and self.key_states[pygame.K_PAGEDOWN]:
            if pygame.K_PAGEDOWN in self.key_timers and current_time - self.key_timers[pygame.K_PAGEDOWN] > self.key_repeat_delay:
                # 增加FOV (缩小) - 增强流畅性
                self.fov = min(self.max_fov, self.fov + 0.8)
                self.update_projection()
                self.key_timers[pygame.K_PAGEDOWN] = current_time - self.key_repeat_delay + self.key_repeat_interval

        # 处理时间流速控制的持续按键
        if keys[pygame.K_EQUALS] or keys[pygame.K_PLUS]:
            if (pygame.K_EQUALS in self.key_states and self.key_states.get(pygame.K_EQUALS, False)) or \
               (pygame.K_PLUS in self.key_states and self.key_states.get(pygame.K_PLUS, False)):
                key = pygame.K_EQUALS if pygame.K_EQUALS in self.key_timers else pygame.K_PLUS
                if key in self.key_timers and current_time - self.key_timers[key] > self.key_repeat_delay:
                    # 持续增加时间流速
                    self.time_scale = min(10.0, self.time_scale * 1.1)
                    if isinstance(self.current_body, PlanetSystem):
                        self.current_body.set_time_scale(self.time_scale)
                    self.key_timers[key] = current_time - self.key_repeat_delay + self.key_repeat_interval * 2

        if keys[pygame.K_MINUS]:
            if pygame.K_MINUS in self.key_states and self.key_states[pygame.K_MINUS]:
                if pygame.K_MINUS in self.key_timers and current_time - self.key_timers[pygame.K_MINUS] > self.key_repeat_delay:
                    # 持续减小时间流速
                    self.time_scale = max(0.1, self.time_scale / 1.1)
                    if isinstance(self.current_body, PlanetSystem):
                        self.current_body.set_time_scale(self.time_scale)
                    self.key_timers[pygame.K_MINUS] = current_time - self.key_repeat_delay + self.key_repeat_interval * 2

    def update(self):
        """更新模拟"""
        if not self.paused:
            dt = 0.016  # 固定时间步长
            self.time += dt * self.time_scale

            # 更新当前天体物理
            start_time = time.time()
            self.current_body.update(dt * self.time_scale)
            physics_time = (time.time() - start_time) * 1000

            # 更新FPS计数
            self.frame_count += 1
            current_time = time.time()
            if current_time - self.last_time >= 1.0:
                fps = self.frame_count / (current_time - self.last_time)
                self.fps_list.append(fps)
                if len(self.fps_list) > 10:
                    self.fps_list.pop(0)
                self.frame_count = 0
                self.last_time = current_time

                # 打印性能信息
                avg_fps = sum(self.fps_list) / len(self.fps_list)

                # 获取粒子数量
                total_particles = 0
                if hasattr(self.current_body, 'disk_particles'):
                    total_particles += len(self.current_body.disk_particles)
                if hasattr(self.current_body, 'jet_particles'):
                    total_particles += len(self.current_body.jet_particles)
                if hasattr(self.current_body, 'particles'):
                    total_particles += len(self.current_body.particles)
                if hasattr(self.current_body, 'corona_particles'):
                    total_particles += len(self.current_body.corona_particles)

                # 显示更详细的性能信息
                mode = "RTX GPU" if CUDA_AVAILABLE and RTX_ENABLED else "CPU"
                print(f"渲染模式: {mode} | 物理计算: {physics_time:.2f}ms | FPS: {int(avg_fps)} | 粒子: {total_particles}")

    def render(self):
        """渲染模拟"""
        # 清除缓冲区
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)

        # 重置模型视图矩阵
        glMatrixMode(GL_MODELVIEW)
        glLoadIdentity()

        # 设置相机位置
        glTranslatef(0.0, 0.0, -self.camera_distance)

        # 应用旋转
        glRotatef(self.rotation_x, 1, 0, 0)
        glRotatef(self.rotation_y, 0, 1, 0)

        # 渲染星空背景
        self.render_stars()

        # 渲染当前天体
        self.current_body.render()

        # 渲染信息
        if self.show_info:
            self.render_info()

        # 渲染帮助
        if self.show_help:
            self.render_help()

        # 交换缓冲区
        pygame.display.flip()

    def render_stars(self):
        """渲染星空背景"""
        # 禁用深度测试
        glDisable(GL_DEPTH_TEST)

        # 启用点平滑
        glEnable(GL_POINT_SMOOTH)
        glHint(GL_POINT_SMOOTH_HINT, GL_NICEST)

        # 启用混合
        glEnable(GL_BLEND)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)

        # 设置点大小
        glPointSize(1.0)

        # 生成随机星星
        np.random.seed(42)  # 固定随机种子，使星星位置固定
        num_stars = 1000

        # 渲染星星
        glBegin(GL_POINTS)
        for i in range(num_stars):
            # 随机位置
            x = np.random.uniform(-100, 100)
            y = np.random.uniform(-100, 100)
            z = np.random.uniform(-100, 100)

            # 随机颜色
            r = np.random.uniform(0.7, 1.0)
            g = np.random.uniform(0.7, 1.0)
            b = np.random.uniform(0.7, 1.0)

            # 添加闪烁效果
            brightness = 0.7 + 0.3 * math.sin(self.time * 2.0 + i * 0.1)

            glColor4f(r * brightness, g * brightness, b * brightness, 1.0)
            glVertex3f(x, y, z)
        glEnd()

        # 禁用混合
        glDisable(GL_BLEND)
        glDisable(GL_POINT_SMOOTH)

        # 启用深度测试
        glEnable(GL_DEPTH_TEST)

    def render_info(self):
        """渲染性能和状态信息"""
        # 计算平均FPS
        avg_fps = int(sum(self.fps_list) / len(self.fps_list)) if self.fps_list else 0

        # 获取渲染模式
        mode = "RTX GPU" if CUDA_AVAILABLE and RTX_ENABLED else "CPU"

        # 获取当前天体信息
        body_info = self.current_body.get_info()
        body_name = body_info["name"]
        body_type = body_info["type"].replace("_", " ") if isinstance(body_info["type"], str) else body_info["type"].name.replace("_", " ")

        # 渲染文本
        y_pos = 10
        self.render_text(f"当前天体: {body_name} ({body_type})", 10, y_pos)
        y_pos += 20
        self.render_text(f"渲染模式: {mode} | FPS: {avg_fps}", 10, y_pos)
        y_pos += 20
        self.render_text(f"相机距离: {self.camera_distance:.1f} | 视场角: {self.fov:.1f}°", 10, y_pos)
        y_pos += 20
        self.render_text(f"时间流速: {'暂停' if self.paused else f'x{self.time_scale:.1f}'}", 10, y_pos)
        y_pos += 20
        self.render_text("按H键显示帮助", 10, y_pos)

    def render_help(self):
        """渲染帮助信息"""
        # 半透明背景
        s = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        s.fill((0, 0, 0, 128))
        self.screen.blit(s, (0, 0))

        # 增强的帮助文本
        help_text = [
            "天体物理模拟程序 - 控制指南",
            "═══════════════════════════════════════",
            "",
            "🎮 视角控制:",
            "- 鼠标左键拖动: 旋转视角",
            "- 鼠标滚轮: 缩放视图",
            "- 上/下箭头: 调整相机距离 (可持续按住)",
            "- 左/右箭头: 水平旋转视角 (可持续按住)",
            "- Page Up/Down: 调整视场角 (可持续按住)",
            "",
            "🌌 天体控制:",
            "- 数字键1-7: 直接切换到指定天体",
            "- Tab: 循环切换天体类型",
            "- 空格: 暂停/继续模拟",
            "- +/= 键: 增加时间流速 (可持续按住)",
            "- - 键: 减少时间流速 (可持续按住)",
            "- R: 重置时间流速为1.0x",
            "",
            "🖥️ 界面控制:",
            "- H: 显示/隐藏帮助",
            "- I: 显示/隐藏状态信息",
            "- ESC: 退出程序",
            "",
            "🌟 天体类型快捷键:",
            "- 1: 克尔黑洞 (有自旋) - 展示框拖效应",
            "- 2: 史瓦西黑洞 (无自旋) - 经典黑洞",
            "- 3: 中子星 - 磁场和辐射",
            "- 4: 主序星 - 稳定恒星",
            "- 5: 红巨星 - 膨胀恒星",
            "- 6: 蓝巨星 - 高温恒星",
            "- 7: 行星系统 - 多天体轨道",
            "",
            "💡 提示:",
            "- 克尔黑洞展示了震撼的旋转效应",
            "- 时间流速可调节观察不同时间尺度",
            "- RTX GPU加速提供最佳视觉效果"
        ]

        # 渲染帮助文本
        y_pos = (self.height - len(help_text) * 20) // 2
        for line in help_text:
            self.render_text(line, (self.width - len(line) * 10) // 2, y_pos)
            y_pos += 20

    def render_text(self, text, x, y):
        """渲染文本 (使用pygame)"""
        font = pygame.font.Font(None, 24)
        text_surface = font.render(text, True, (255, 255, 255))
        self.screen.blit(text_surface, (x, y))

    def run(self):
        """运行模拟"""
        print("开始运行模拟")
        print("按H键显示帮助")
        try:
            while self.running:
                self.handle_events()
                self.update()
                self.render()
                self.clock.tick(60)
        except Exception as e:
            print(f"运行时错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            pygame.quit()
            print("天体物理模拟程序已退出")

if __name__ == "__main__":
    sim = Simulation()
    sim.run()
