@echo off
chcp 65001 > nul
echo ========================================================
echo Celestial Physics Simulator - RTX 4060 GPU Accelerated
echo ========================================================
echo.

:: Enable CUDA and RTX acceleration
echo Enabling CUDA and RTX GPU acceleration...
set CUDA_VISIBLE_DEVICES=0
set NUMBA_ENABLE_CUDASIM=0
set FORCE_CPU=0

:: Set NVIDIA environment variables
set __NV_PRIME_RENDER_OFFLOAD=1
set __GLX_VENDOR_LIBRARY_NAME=nvidia
set __VK_LAYER_NV_optimus=NVIDIA_only

:: Start the simulation
echo.
echo Starting celestial physics simulation...
echo Press H key to display help information
echo.
python black_hole_simulator.py

echo.
echo Program exited
pause
