#!/usr/bin/env python3
"""
黑洞模拟程序科学准确升级测试脚本
验证科学准确性和教育价值
"""

import sys
import time

def test_scientific_accuracy():
    """测试科学准确性"""
    print("🔬 测试科学准确性...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ 科学准确性检查:")
        
        # 测试克尔黑洞的物理参数
        kerr_bh = bhs.KerrBlackHole(0, 0, 0, 1e6, 0.998, "测试克尔黑洞")
        
        # 检查ISCO和光子球半径
        if hasattr(kerr_bh, 'isco_radius') and hasattr(kerr_bh, 'photon_sphere_radius'):
            print(f"   ✅ ISCO半径: {kerr_bh.isco_radius:.2f}")
            print(f"   ✅ 光子球半径: {kerr_bh.photon_sphere_radius:.2f}")
            
            # 验证物理关系: 光子球 < ISCO
            if kerr_bh.photon_sphere_radius < kerr_bh.isco_radius:
                print("   ✅ 物理关系正确: 光子球 < ISCO")
            else:
                print("   ❌ 物理关系错误")
                return False
        
        # 检查自旋参数范围
        if 0 <= kerr_bh.spin_parameter <= 0.998:
            print(f"   ✅ 自旋参数在物理范围内: {kerr_bh.spin_parameter}")
        else:
            print("   ❌ 自旋参数超出物理范围")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 科学准确性测试失败: {e}")
        return False

def test_neutron_star_physics():
    """测试中子星物理"""
    print("\n🌟 测试中子星物理...")
    try:
        import black_hole_simulator as bhs
        
        # 创建中子星
        neutron_star = bhs.NeutronStar(0, 0, 0, 2.0*bhs.M_SUN, 10000, "测试中子星")
        
        print("✅ 中子星物理检查:")
        
        # 检查物理参数
        if hasattr(neutron_star, 'rotation_period'):
            print(f"   ✅ 自转周期: {neutron_star.rotation_period}s")
        
        if hasattr(neutron_star, 'magnetic_field'):
            print(f"   ✅ 磁场强度: {neutron_star.magnetic_field:.0e}T")
        
        if hasattr(neutron_star, 'surface_temperature'):
            print(f"   ✅ 表面温度: {neutron_star.surface_temperature:.0e}K")
        
        # 检查脉冲星特征
        if hasattr(neutron_star, 'pulse_period') and hasattr(neutron_star, 'beam_angle'):
            print(f"   ✅ 脉冲周期: {neutron_star.pulse_period}s")
            print(f"   ✅ 射电束角度: {neutron_star.beam_angle}°")
        
        # 检查科学方法
        scientific_methods = [
            'generate_magnetic_field_lines',
            'generate_radio_beams',
            'render_magnetic_field_lines',
            'render_radio_beams',
            'temperature_to_rgb'
        ]
        
        for method in scientific_methods:
            if hasattr(neutron_star, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} 缺失")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 中子星物理测试失败: {e}")
        return False

def test_stellar_physics():
    """测试恒星物理"""
    print("\n⭐ 测试恒星物理...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ 恒星物理检查:")
        
        # 测试主序星
        main_star = bhs.MainSequenceStar(0, 0, 0, 1.0*bhs.M_SUN, "测试主序星")
        print(f"   ✅ 主序星温度: {main_star.temperature}K")
        
        # 测试红巨星
        red_giant = bhs.RedGiantStar(0, 0, 0, 1.5*bhs.M_SUN, "测试红巨星")
        print(f"   ✅ 红巨星温度: {red_giant.temperature}K")
        
        # 测试蓝巨星
        blue_giant = bhs.BlueGiantStar(0, 0, 0, 20.0*bhs.M_SUN, "测试蓝巨星")
        print(f"   ✅ 蓝巨星温度: {blue_giant.temperature}K")
        
        # 验证温度关系: 蓝巨星 > 主序星 > 红巨星
        if blue_giant.temperature > main_star.temperature > red_giant.temperature:
            print("   ✅ 恒星温度关系正确")
        else:
            print("   ❌ 恒星温度关系错误")
            return False
        
        # 检查颜色计算
        if hasattr(main_star, 'temperature_to_rgb'):
            color = main_star.temperature_to_rgb(5800)  # 太阳温度
            print(f"   ✅ 太阳颜色 (5800K): RGB{color}")
        
        return True
    except Exception as e:
        print(f"❌ 恒星物理测试失败: {e}")
        return False

def test_performance_optimization():
    """测试性能优化"""
    print("\n🚀 测试性能优化...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ 性能优化检查:")
        
        # 检查粒子数量优化
        print(f"   RTX粒子数: {bhs.MAX_PARTICLES_RTX}")
        print(f"   标准粒子数: {bhs.MAX_PARTICLES_STANDARD}")
        
        # 验证粒子数量合理
        if bhs.MAX_PARTICLES_RTX <= 1000 and bhs.MAX_PARTICLES_STANDARD <= 600:
            print("   ✅ 粒子数量已优化")
        else:
            print("   ⚠️  粒子数量可能过高")
        
        # 检查性能监控
        performance_methods = [
            'check_performance',
            'auto_reduce_particles',
            'auto_increase_particles'
        ]
        
        for method in performance_methods:
            if hasattr(bhs.Simulation, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} 缺失")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 性能优化测试失败: {e}")
        return False

def test_educational_value():
    """测试教育价值"""
    print("\n📚 测试教育价值...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ 教育价值检查:")
        
        # 检查物理常数
        constants = ['G', 'C', 'M_SUN', 'R_SUN', 'AU']
        for const in constants:
            if hasattr(bhs, const):
                value = getattr(bhs, const)
                print(f"   ✅ {const}: {value:.2e}")
            else:
                print(f"   ❌ {const} 缺失")
                return False
        
        # 检查天体类型
        celestial_types = [
            'KerrBlackHole',
            'SchwarzschildBlackHole', 
            'NeutronStar',
            'MainSequenceStar',
            'RedGiantStar',
            'BlueGiantStar',
            'PlanetSystem'
        ]
        
        for celestial_type in celestial_types:
            if hasattr(bhs, celestial_type):
                print(f"   ✅ {celestial_type}")
            else:
                print(f"   ❌ {celestial_type} 缺失")
                return False
        
        print("   ✅ 涵盖完整的天体物理学概念")
        return True
    except Exception as e:
        print(f"❌ 教育价值测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 黑洞模拟程序科学准确升级测试")
    print("=" * 60)
    
    tests = [
        test_scientific_accuracy,
        test_neutron_star_physics,
        test_stellar_physics,
        test_performance_optimization,
        test_educational_value
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.3)
    
    print("\n" + "=" * 60)
    print(f"🏆 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 科学准确升级测试通过！")
        print("\n🔬 科学价值体现:")
        print("   • 基于真实物理方程和观测数据")
        print("   • 遵循广义相对论和恒星物理学")
        print("   • 展示脉冲星、黑洞等前沿天体物理")
        print("   • 提供准确的物理参数和关系")
        print("   • 适合科学教育和研究可视化")
        print("\n🎯 教育意义:")
        print("   • 直观理解复杂物理概念")
        print("   • 激发对天体物理学的兴趣")
        print("   • 培养科学思维和观察能力")
        print("   • 连接理论与观测现象")
        print("\n🚀 运行程序体验科学升级:")
        print("   python black_hole_simulator.py")
        print("\n💡 科学观察提示:")
        print("   • 按3键观察中子星的脉冲效应")
        print("   • 按1键观察克尔黑洞的框拖效应")
        print("   • 注意不同恒星的温度-颜色关系")
        print("   • 观察行星系统的开普勒运动")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
