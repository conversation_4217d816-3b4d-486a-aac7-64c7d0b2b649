# 黑洞模拟程序

这个程序模拟了一个具有真实感的黑洞，包括事件视界、光子球、吸积盘和相对论性喷流。模拟基于物理学原理，展示了黑洞周围的引力效应和相对论现象，灵感来源于《星际穿越》电影中的黑洞视觉效果。

## 特性

- **黑洞可视化**：
  - 事件视界（黑色球体）
  - 光子球（蓝色半透明球体）
  - 动态吸积盘
  - 相对论性喷流（对于高自旋黑洞）
  - 星空背景

- **物理模型**：
  - 基于开普勒轨道的吸积盘粒子运动
  - 相对论性喷流模拟
  - 引力透镜效应
  - 基于物理学的粒子行为

- **交互式控制**：
  - 鼠标控制旋转视角
  - 键盘控制相机距离
  - 实时FPS显示

## 要求

- Python 3.x
- Pygame
- NumPy
- PyOpenGL
- Numba (可选，用于CUDA加速)

## 安装

1. 确保您的系统上安装了Python
2. 安装所需的包：
   ```
   pip install pygame numpy pyopengl numba
   ```

## 如何运行

使用批处理文件运行程序：
```
.\run.bat
```

或者直接运行Python脚本：
```
python black_hole_simulator.py
```

## 控制方式

### 视角控制
- **鼠标左键拖动**：旋转视角
- **鼠标滚轮**：缩放视图
- **上/下箭头**：调整相机距离（放大/缩小）
- **左/右箭头**：水平旋转视角
- **Page Up/Down**：调整视场角（FOV）

### 天体控制
- **数字键1-7**：直接切换到指定天体
- **Tab键**：循环切换天体类型
- **空格键**：暂停/继续模拟
- **+/-键**：增加/减少时间流速
- **R键**：重置时间流速

### 界面控制
- **H键**：显示/隐藏帮助
- **I键**：显示/隐藏信息
- **ESC键**：退出模拟

### 天体类型快捷键
- **1**：克尔黑洞（有自旋）
- **2**：史瓦西黑洞（无自旋）
- **3**：中子星
- **4**：主序星
- **5**：红巨星
- **6**：蓝巨星
- **7**：行星系统

## 物理模型

模拟使用了以下物理模型：
- 基于开普勒轨道的粒子运动
- 黑洞引力效应
- 引力透镜效应
- 相对论性喷流（对于高自旋黑洞）

## 自定义

您可以在代码中修改以下参数：
- 黑洞质量和自旋参数
- 吸积盘和背景星星的密度
- 视觉效果和渲染设置

## 技术细节

- 使用OpenGL进行3D渲染
- 使用Pygame处理窗口和事件
- 使用NumPy进行数值计算
- 高效的粒子系统实现

## 故障排除

如果程序无法正常运行，请尝试以下解决方案：

1. 确保已安装所有必要的依赖项
2. 更新显卡驱动到最新版本
3. 关闭其他占用资源的程序
4. 减少模拟中的粒子数量以提高性能
