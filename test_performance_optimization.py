#!/usr/bin/env python3
"""
黑洞模拟程序性能优化测试脚本
验证性能优化和视觉增强效果
"""

import sys
import time

def test_performance_constants():
    """测试性能优化常数"""
    print("🚀 测试性能优化常数...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ 性能常数检查:")
        print(f"   RTX粒子数: {bhs.MAX_PARTICLES_RTX}")
        print(f"   标准粒子数: {bhs.MAX_PARTICLES_STANDARD}")
        print(f"   RTX轨迹长度: {bhs.TRAIL_LENGTH_RTX}")
        print(f"   标准轨迹长度: {bhs.TRAIL_LENGTH_STANDARD}")
        print(f"   性能模式: {bhs.PERFORMANCE_MODE}")
        print(f"   目标FPS: {bhs.TARGET_FPS}")
        print(f"   最低FPS阈值: {bhs.MIN_FPS_THRESHOLD}")
        
        return True
    except Exception as e:
        print(f"❌ 性能常数测试失败: {e}")
        return False

def test_enhanced_rotation():
    """测试增强的旋转效果"""
    print("\n🌀 测试增强旋转效果...")
    try:
        import black_hole_simulator as bhs
        
        # 创建克尔黑洞
        kerr_bh = bhs.KerrBlackHole(0, 0, 0, 1e6, 0.998, "测试克尔黑洞")
        
        print("✅ 旋转效果检查:")
        print(f"   自旋参数: {kerr_bh.spin_parameter}")
        print(f"   初始旋转角度: {getattr(kerr_bh, 'rotation_angle', 0)}")
        
        # 检查粒子数量是否已优化
        disk_count = len(kerr_bh.disk_particles)
        jet_count = len(kerr_bh.jet_particles)
        print(f"   优化后吸积盘粒子: {disk_count}")
        print(f"   优化后喷流粒子: {jet_count}")
        
        # 验证粒子数量在合理范围内
        if disk_count <= 600 and jet_count <= 200:
            print("   ✅ 粒子数量已优化，性能友好")
        else:
            print("   ⚠️  粒子数量可能过高")
        
        return True
    except Exception as e:
        print(f"❌ 旋转效果测试失败: {e}")
        return False

def test_simulation_features():
    """测试模拟器新功能"""
    print("\n🎮 测试模拟器新功能...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ 新功能检查:")
        
        # 检查性能监控方法
        performance_methods = [
            'check_performance',
            'auto_reduce_particles',
            'auto_increase_particles'
        ]
        
        for method in performance_methods:
            if hasattr(bhs.Simulation, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - 缺失")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 模拟器功能测试失败: {e}")
        return False

def test_visual_enhancements():
    """测试视觉增强效果"""
    print("\n🎨 测试视觉增强效果...")
    try:
        import black_hole_simulator as bhs
        
        # 创建克尔黑洞测试视觉效果
        kerr_bh = bhs.KerrBlackHole(0, 0, 0, 1e6, 0.998, "测试克尔黑洞")
        
        print("✅ 视觉增强检查:")
        
        # 检查自旋参数
        if kerr_bh.spin_parameter > 0.9:
            print(f"   ✅ 高自旋参数: {kerr_bh.spin_parameter} (震撼旋转效果)")
        
        # 检查粒子系统
        if hasattr(kerr_bh, 'disk_particles') and len(kerr_bh.disk_particles) > 0:
            print(f"   ✅ 吸积盘粒子系统: {len(kerr_bh.disk_particles)} 个粒子")
        
        if hasattr(kerr_bh, 'jet_particles') and len(kerr_bh.jet_particles) > 0:
            print(f"   ✅ 喷流粒子系统: {len(kerr_bh.jet_particles)} 个粒子")
        
        # 检查渲染方法
        render_methods = ['render', 'update']
        for method in render_methods:
            if hasattr(kerr_bh, method):
                print(f"   ✅ {method} 方法")
            else:
                print(f"   ❌ {method} 方法缺失")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 视觉增强测试失败: {e}")
        return False

def test_cuda_optimization():
    """测试CUDA优化"""
    print("\n⚡ 测试CUDA优化...")
    try:
        import black_hole_simulator as bhs
        
        print("✅ CUDA优化检查:")
        print(f"   CUDA可用: {bhs.CUDA_AVAILABLE}")
        print(f"   RTX启用: {bhs.RTX_ENABLED}")
        
        if bhs.CUDA_AVAILABLE:
            print("   ✅ GPU加速已启用")
            
            # 检查CUDA函数
            cuda_functions = [
                'update_particle_positions_cuda',
                'update_particle_velocities_cuda',
                'calculate_orbital_motion_cuda'
            ]
            
            for func in cuda_functions:
                if hasattr(bhs, func):
                    print(f"   ✅ {func}")
                else:
                    print(f"   ❌ {func} 缺失")
                    return False
        else:
            print("   ⚠️  CUDA不可用，使用CPU模式")
        
        return True
    except Exception as e:
        print(f"❌ CUDA优化测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌟 黑洞模拟程序性能优化测试")
    print("=" * 50)
    
    tests = [
        test_performance_constants,
        test_enhanced_rotation,
        test_simulation_features,
        test_visual_enhancements,
        test_cuda_optimization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.3)
    
    print("\n" + "=" * 50)
    print(f"🏆 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 性能优化测试通过！")
        print("\n✨ 优化效果:")
        print("   • 粒子数量已优化，提升帧率")
        print("   • 旋转效果极大增强，更加明显")
        print("   • 自动性能调整系统已启用")
        print("   • CUDA加速功能完整")
        print("   • 视觉效果震撼升级")
        print("\n🚀 运行程序体验优化效果:")
        print("   python black_hole_simulator.py")
        print("\n💡 使用提示:")
        print("   • 按1键切换到克尔黑洞观察震撼旋转")
        print("   • 程序会自动调整粒子数量优化性能")
        print("   • 旋转速度已大幅增加，效果更明显")
        print("   • 按Q键切换渲染质量模式")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
