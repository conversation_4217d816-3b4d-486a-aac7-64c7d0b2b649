# 天体物理模拟程序 - 全方位升级版

这是一个基于RTX 4060 GPU优化的高级天体物理模拟程序，展示了震撼的黑洞、恒星和行星系统。程序采用最新的物理模型和视觉效果，提供电影级的科学可视化体验，灵感来源于《星际穿越》等科幻电影。

## 🌟 核心特性

### 🕳️ 黑洞模拟
- **克尔黑洞（有自旋）**：
  - 震撼的旋转可视化效果
  - 框拖效应和时空扭曲
  - 多层自旋指示器
  - 中心旋转涡流效果
  - 时空网格扭曲可视化
- **史瓦西黑洞（无自旋）**：
  - 经典黑洞模型
  - 事件视界和光子球
  - 引力透镜效应
- **高级物理效应**：
  - 爱因斯坦环和引力弧
  - 时间膨胀和红移效应
  - 引力波可视化
  - 背景星空扭曲

### ⭐ 恒星系统
- **多种恒星类型**：
  - 主序星、红巨星、蓝巨星
  - 基于温度的真实颜色
  - 动态日冕和恒星大气
  - 表面纹理和光照效果
- **中子星**：
  - 磁场可视化
  - 高能粒子辐射
  - 快速自转效应

### 🪐 行星系统
- **多天体轨道模拟**：
  - 真实的开普勒轨道
  - 行星大小和颜色变化
  - 轨道周期可视化

## 🚀 技术特性

### 💻 GPU加速
- **RTX 4060专用优化**：
  - CUDA并行计算
  - 最大化GPU利用率
  - 高性能粒子系统
  - 实时物理计算

### 🎮 增强控制
- **优化的响应性**：
  - 减少按键延迟
  - 流畅的持续按键支持
  - 立即反馈机制
  - 智能按键状态管理

### 🎨 视觉效果
- **电影级渲染**：
  - 高质量粒子系统
  - 动态光照和阴影
  - 多层混合效果
  - 实时动画和特效

## 要求

- Python 3.x
- Pygame
- NumPy
- PyOpenGL
- Numba (可选，用于CUDA加速)

## 安装

1. 确保您的系统上安装了Python
2. 安装所需的包：
   ```
   pip install pygame numpy pyopengl numba
   ```

## 如何运行

使用批处理文件运行程序：
```
.\run.bat
```

或者直接运行Python脚本：
```
python black_hole_simulator.py
```

## 🎮 控制方式

### 🖱️ 视角控制
- **鼠标左键拖动**：旋转视角
- **鼠标滚轮**：缩放视图
- **上/下箭头**：调整相机距离（可持续按住）
- **左/右箭头**：水平旋转视角（可持续按住）
- **Page Up/Down**：调整视场角（可持续按住）

### 🌌 天体控制
- **数字键1-7**：直接切换到指定天体
- **Tab键**：循环切换天体类型
- **空格键**：暂停/继续模拟
- **+/= 键**：增加时间流速（可持续按住）
- **- 键**：减少时间流速（可持续按住）
- **R键**：重置时间流速为1.0x

### 🖥️ 界面控制
- **H键**：显示/隐藏帮助
- **I键**：显示/隐藏状态信息
- **ESC键**：退出程序

### 🌟 天体类型快捷键
- **1**：克尔黑洞（有自旋）- 展示框拖效应
- **2**：史瓦西黑洞（无自旋）- 经典黑洞
- **3**：中子星 - 磁场和辐射
- **4**：主序星 - 稳定恒星
- **5**：红巨星 - 膨胀恒星
- **6**：蓝巨星 - 高温恒星
- **7**：行星系统 - 多天体轨道

### 💡 控制提示
- **持续按键**：方向键、Page Up/Down、+/- 键支持持续按住
- **立即响应**：所有快捷键都有立即反馈
- **流畅操作**：优化的按键延迟和重复间隔

## 🔬 物理模型

### 黑洞物理
- **广义相对论效应**：
  - 史瓦西度规和克尔度规
  - 事件视界和光子球计算
  - 最后稳定圆轨道（ISCO）
- **框拖效应**：
  - 时空扭曲可视化
  - 自旋参数影响
  - 粒子轨道偏转
- **引力透镜**：
  - 爱因斯坦环形成
  - 引力弧扭曲
  - 背景星空变形
- **时间膨胀**：
  - 红移效应
  - 粒子时间流速变化
  - 相对论性颜色变化

### 恒星物理
- **黑体辐射**：
  - 基于温度的真实颜色
  - 斯特藩-玻尔兹曼定律
- **恒星大气**：
  - 日冕粒子模拟
  - 磁场效应（中子星）
  - 恒星风和物质抛射

### 轨道力学
- **开普勒轨道**：
  - 椭圆轨道计算
  - 轨道周期和偏心率
- **多体问题**：
  - 引力相互作用
  - 轨道稳定性

## ⚙️ 技术架构

### 渲染引擎
- **OpenGL 3D渲染**：
  - 硬件加速图形
  - 多层混合效果
  - 实时光照和阴影
- **粒子系统**：
  - GPU并行计算
  - 动态粒子生成
  - 轨迹追踪和渲染

### 计算优化
- **CUDA加速**：
  - 并行物理计算
  - GPU内存管理
  - 实时性能监控
- **数值计算**：
  - NumPy向量化操作
  - 高精度物理模拟
  - 自适应时间步长

### 用户界面
- **Pygame事件系统**：
  - 优化的输入处理
  - 实时状态反馈
  - 智能按键管理

## 🔧 故障排除

### GPU相关问题
如果遇到CUDA或RTX相关错误：

1. **检查CUDA安装**：
   ```bash
   nvidia-smi  # 检查NVIDIA驱动
   nvcc --version  # 检查CUDA版本
   ```

2. **更新驱动程序**：
   - 下载最新的NVIDIA驱动
   - 确保支持CUDA 11.0+

3. **验证GPU检测**：
   - 程序启动时会显示GPU信息
   - 确认RTX 4060被正确识别

### 性能优化
如果遇到性能问题：

1. **降低粒子数量**：
   - 程序会根据GPU能力自动调整
   - RTX模式：2000粒子
   - 标准模式：1000粒子

2. **关闭其他程序**：
   - 释放GPU内存
   - 确保足够的系统资源

3. **调整时间流速**：
   - 使用+/-键调节
   - 降低时间流速可提高稳定性

### 常见错误解决
- **"CUDA不可用"**：安装numba和CUDA工具包
- **"程序需要GPU加速"**：确保NVIDIA GPU正常工作
- **低帧率**：检查GPU利用率，可能需要更新驱动
- **按键不响应**：重启程序，检查键盘连接

### 系统要求
- **最低要求**：NVIDIA GTX 1060或更高
- **推荐配置**：RTX 4060或更高
- **内存要求**：8GB RAM，4GB VRAM
- **操作系统**：Windows 10/11，支持CUDA
